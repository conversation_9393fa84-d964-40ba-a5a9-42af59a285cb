<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Debug Jobs - Check Published Jobs</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            padding: 20px;
            background: #f5f5f5;
        }
        .container {
            max-width: 800px;
            margin: 0 auto;
            background: white;
            padding: 20px;
            border-radius: 10px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        .job-item {
            border: 1px solid #ddd;
            padding: 15px;
            margin: 10px 0;
            border-radius: 8px;
            background: #f9f9f9;
        }
        .btn {
            background: #d17e7e;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 5px;
            cursor: pointer;
            margin: 5px;
        }
        .btn:hover {
            background: #c16868;
        }
        .clear-btn {
            background: #dc3545;
        }
        .clear-btn:hover {
            background: #c82333;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🔍 Complete Debug Dashboard</h1>

        <div style="margin-bottom: 20px;">
            <button class="btn" onclick="loadJobs()">🔄 Refresh Jobs</button>
            <button class="btn" onclick="showUserInfo()">👤 Show User Info</button>
            <button class="btn" onclick="testJobPublishing()">📝 Test Job Publishing</button>
            <button class="btn clear-btn" onclick="clearJobs()">🗑️ Clear All Jobs</button>
            <button class="btn" onclick="simulateEmployerLogin()">🏢 Simulate Employer Login</button>
        </div>

        <div id="statusContainer" style="background: #f0f0f0; padding: 15px; border-radius: 8px; margin-bottom: 20px;">
            <h3>📊 Real-time Status</h3>
            <div id="statusInfo">Loading...</div>
        </div>

        <div id="jobsContainer">
            <p>Click "Refresh Jobs" to see published jobs</p>
        </div>
    </div>

    <script>
        function loadJobs() {
            updateStatus(); // Update status when loading jobs

            // Get jobs from new local manager if available
            let jobs = [];
            if (window.localJobsManager) {
                jobs = window.localJobsManager.getAllJobs();
                console.log('📋 Using Local Jobs Manager');
            } else {
                jobs = JSON.parse(localStorage.getItem('jobs') || '[]');
                console.log('📋 Using direct localStorage');
            }

            const container = document.getElementById('jobsContainer');

            if (jobs.length === 0) {
                container.innerHTML = '<p>❌ No jobs found</p>';
                return;
            }

            let html = `<h3>✅ Found ${jobs.length} jobs:</h3>`;
            jobs.forEach((job, index) => {
                const hasEmployerInfo = job.employerName && job.employerEmail;
                const statusIcon = hasEmployerInfo ? '✅' : '❌';
                const source = job.source || 'unknown';

                html += `
                    <div class="job-item" style="border-left: 4px solid ${hasEmployerInfo ? '#28a745' : '#dc3545'};">
                        <h4>${statusIcon} ${job.title}</h4>
                        <p><strong>ID:</strong> ${job.id || 'No ID'}</p>
                        <p><strong>Category:</strong> ${job.category}</p>
                        <p><strong>Category Title:</strong> ${job.categoryTitle}</p>
                        <p><strong>Employer:</strong> ${job.employerName || '❌ MISSING'}</p>
                        <p><strong>Email:</strong> ${job.employerEmail || '❌ MISSING'}</p>
                        <p><strong>Description:</strong> ${job.description}</p>
                        <p><strong>Payment:</strong> ${job.paymentCode}</p>
                        <p><strong>Hours:</strong> ${job.expectedHours}</p>
                        <p><strong>Source:</strong> ${source}</p>
                        <p><strong>Published:</strong> ${job.createdAt ? new Date(job.createdAt).toLocaleString() : new Date(job.timestamp).toLocaleString()}</p>
                        <button class="btn clear-btn" onclick="deleteJob('${job.id || index}')">Delete This Job</button>
                    </div>
                `;
            });

            container.innerHTML = html;
        }
        
        function clearJobs() {
            if (confirm('Are you sure you want to clear all jobs?')) {
                localStorage.removeItem('jobs');
                loadJobs();
                alert('All jobs cleared!');
            }
        }
        
        function deleteJob(index) {
            const jobs = JSON.parse(localStorage.getItem('jobs') || '[]');
            jobs.splice(index, 1);
            localStorage.setItem('jobs', JSON.stringify(jobs));
            loadJobs();
            alert('Job deleted!');
        }
        
        function addTestJob() {
            const testJob = {
                title: "Test Legal Research Job",
                description: "This is a test job for legal research assistance",
                paymentCode: "R500/hour",
                expectedHours: "10 hours/week",
                category: "legal-research",
                categoryTitle: "Legal Research Assistance",
                employerName: "Test Law Firm",
                employerEmail: "<EMAIL>",
                timestamp: Date.now()
            };

            const jobs = JSON.parse(localStorage.getItem('jobs') || '[]');
            jobs.push(testJob);
            localStorage.setItem('jobs', JSON.stringify(jobs));
            loadJobs();
            alert('Test job added!');
        }

        function showUserInfo() {
            const employerInfo = JSON.parse(localStorage.getItem('employerInfo') || '{}');
            const currentUser = JSON.parse(localStorage.getItem('currentUser') || '{}');

            const container = document.getElementById('jobsContainer');
            container.innerHTML = `
                <h3>User Information Debug</h3>
                <div class="job-item">
                    <h4>EmployerInfo (localStorage key: 'employerInfo')</h4>
                    <pre>${JSON.stringify(employerInfo, null, 2)}</pre>
                </div>
                <div class="job-item">
                    <h4>CurrentUser (localStorage key: 'currentUser')</h4>
                    <pre>${JSON.stringify(currentUser, null, 2)}</pre>
                </div>
                <div class="job-item">
                    <h4>All localStorage Keys</h4>
                    <p>${Object.keys(localStorage).join(', ')}</p>
                </div>
            `;
        }
        
        function updateStatus() {
            const employerInfo = JSON.parse(localStorage.getItem('employerInfo') || '{}');
            const currentUser = JSON.parse(localStorage.getItem('currentUser') || '{}');
            const jobs = JSON.parse(localStorage.getItem('jobs') || '[]');

            const statusInfo = document.getElementById('statusInfo');
            statusInfo.innerHTML = `
                <div style="display: grid; grid-template-columns: 1fr 1fr; gap: 15px;">
                    <div>
                        <strong>👤 Current User:</strong><br>
                        Name: ${currentUser.name || 'Not set'}<br>
                        Email: ${currentUser.email || 'Not set'}<br>
                        Type: ${currentUser.userType || 'Not set'}
                    </div>
                    <div>
                        <strong>🏢 Employer Info:</strong><br>
                        Name: ${employerInfo.name || 'Not set'}<br>
                        Email: ${employerInfo.email || 'Not set'}<br>
                        Type: ${employerInfo.type || 'Not set'}
                    </div>
                </div>
                <div style="margin-top: 10px;">
                    <strong>📊 Jobs Count:</strong> ${jobs.length} jobs in storage<br>
                    <strong>🔑 LocalStorage Keys:</strong> ${Object.keys(localStorage).join(', ')}
                </div>
            `;
        }

        function simulateEmployerLogin() {
            const email = prompt('Enter employer email:') || '<EMAIL>';
            const name = prompt('Enter employer name:') || 'Test Employer';

            const employerData = {
                name: name,
                email: email,
                userType: 'employer',
                type: 'Law Firm'
            };

            localStorage.setItem('currentUser', JSON.stringify(employerData));
            localStorage.setItem('employerInfo', JSON.stringify(employerData));

            alert('✅ Employer login simulated!');
            updateStatus();
        }

        function testJobPublishing() {
            const employerInfo = JSON.parse(localStorage.getItem('employerInfo') || '{}');
            const currentUser = JSON.parse(localStorage.getItem('currentUser') || '{}');

            if (!employerInfo.name && !currentUser.name) {
                alert('❌ No employer logged in! Click "Simulate Employer Login" first.');
                return;
            }

            const employer = {
                name: employerInfo.name || currentUser.name || '',
                email: employerInfo.email || currentUser.email || ''
            };

            // Test all 4 categories that employers can publish to
            const categories = [
                { id: 'legal-filings', title: 'Preparing Legal Filings & Case Documents' },
                { id: 'document-summary', title: 'Summarizing Legal Documents' },
                { id: 'corporate-advisory', title: 'Corporate Legal Advisory Support' },
                { id: 'legal-research', title: 'Legal Research Assistance' }
            ];

            const selectedCategory = categories[Math.floor(Math.random() * categories.length)];

            const testJob = {
                title: `${selectedCategory.title} - Test Job ${new Date().toLocaleTimeString()}`,
                description: `This is a test job for ${selectedCategory.title.toLowerCase()}. Created through debug system.`,
                paymentCode: "R600/hour",
                expectedHours: "15 hours/week",
                category: selectedCategory.id,
                categoryTitle: selectedCategory.title,
                employerName: employer.name,
                employerEmail: employer.email,
                timestamp: Date.now()
            };

            console.log('🔍 Creating job with employer info:', employer);
            console.log('🔍 Job object:', testJob);

            const jobs = JSON.parse(localStorage.getItem('jobs') || '[]');
            jobs.push(testJob);
            localStorage.setItem('jobs', JSON.stringify(jobs));

            alert(`✅ Test job published!\nCategory: ${selectedCategory.id}\nTitle: ${selectedCategory.title}\nEmployer: ${employer.name}\nEmail: ${employer.email}\n\nNow test in student view!`);
            loadJobs();
            updateStatus();
        }

        // Load jobs and status on page load
        window.onload = function() {
            updateStatus();
            loadJobs();
        };
    </script>

    <!-- Debug Script -->
    <script src="debug-jobs.js"></script>
    <!-- Local Jobs Management -->
    <script src="local-jobs-manager.js"></script>
</body>
</html>
