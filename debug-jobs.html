<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Debug Jobs - Check Published Jobs</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            padding: 20px;
            background: #f5f5f5;
        }
        .container {
            max-width: 800px;
            margin: 0 auto;
            background: white;
            padding: 20px;
            border-radius: 10px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        .job-item {
            border: 1px solid #ddd;
            padding: 15px;
            margin: 10px 0;
            border-radius: 8px;
            background: #f9f9f9;
        }
        .btn {
            background: #d17e7e;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 5px;
            cursor: pointer;
            margin: 5px;
        }
        .btn:hover {
            background: #c16868;
        }
        .clear-btn {
            background: #dc3545;
        }
        .clear-btn:hover {
            background: #c82333;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>Debug Jobs - Check Published Jobs</h1>
        
        <div style="margin-bottom: 20px;">
            <button class="btn" onclick="loadJobs()">Refresh Jobs</button>
            <button class="btn clear-btn" onclick="clearJobs()">Clear All Jobs</button>
            <button class="btn" onclick="addTestJob()">Add Test Job</button>
        </div>
        
        <div id="jobsContainer">
            <p>Click "Refresh Jobs" to see published jobs</p>
        </div>
    </div>

    <script>
        function loadJobs() {
            const jobs = JSON.parse(localStorage.getItem('jobs') || '[]');
            const container = document.getElementById('jobsContainer');
            
            if (jobs.length === 0) {
                container.innerHTML = '<p>No jobs found in localStorage</p>';
                return;
            }
            
            let html = `<h3>Found ${jobs.length} jobs:</h3>`;
            jobs.forEach((job, index) => {
                html += `
                    <div class="job-item">
                        <h4>${job.title}</h4>
                        <p><strong>Category:</strong> ${job.category}</p>
                        <p><strong>Category Title:</strong> ${job.categoryTitle}</p>
                        <p><strong>Employer:</strong> ${job.employerName}</p>
                        <p><strong>Email:</strong> ${job.employerEmail}</p>
                        <p><strong>Description:</strong> ${job.description}</p>
                        <p><strong>Payment:</strong> ${job.paymentCode}</p>
                        <p><strong>Hours:</strong> ${job.expectedHours}</p>
                        <p><strong>Published:</strong> ${new Date(job.timestamp).toLocaleString()}</p>
                        <button class="btn clear-btn" onclick="deleteJob(${index})">Delete This Job</button>
                    </div>
                `;
            });
            
            container.innerHTML = html;
        }
        
        function clearJobs() {
            if (confirm('Are you sure you want to clear all jobs?')) {
                localStorage.removeItem('jobs');
                loadJobs();
                alert('All jobs cleared!');
            }
        }
        
        function deleteJob(index) {
            const jobs = JSON.parse(localStorage.getItem('jobs') || '[]');
            jobs.splice(index, 1);
            localStorage.setItem('jobs', JSON.stringify(jobs));
            loadJobs();
            alert('Job deleted!');
        }
        
        function addTestJob() {
            const testJob = {
                title: "Test Legal Research Job",
                description: "This is a test job for legal research assistance",
                paymentCode: "R500/hour",
                expectedHours: "10 hours/week",
                category: "legal-research",
                categoryTitle: "Legal Research Assistance",
                employerName: "Test Law Firm",
                employerEmail: "<EMAIL>",
                timestamp: Date.now()
            };
            
            const jobs = JSON.parse(localStorage.getItem('jobs') || '[]');
            jobs.push(testJob);
            localStorage.setItem('jobs', JSON.stringify(jobs));
            loadJobs();
            alert('Test job added!');
        }
        
        // Load jobs on page load
        window.onload = loadJobs;
    </script>
</body>
</html>
