<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Firebase Auth Demo - Create Test Users</title>
    <style>
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
            background: #f5f5f5;
        }
        .container {
            background: white;
            padding: 30px;
            border-radius: 10px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        .section {
            margin: 30px 0;
            padding: 20px;
            border: 1px solid #ddd;
            border-radius: 8px;
        }
        .section h3 {
            margin-top: 0;
            color: #333;
        }
        .form-group {
            margin: 15px 0;
        }
        label {
            display: block;
            margin-bottom: 5px;
            font-weight: 500;
            color: #333;
        }
        input, select {
            width: 100%;
            padding: 10px;
            border: 1px solid #ddd;
            border-radius: 5px;
            font-size: 14px;
        }
        button {
            background: #d17e7e;
            color: white;
            border: none;
            padding: 12px 24px;
            border-radius: 6px;
            cursor: pointer;
            font-size: 14px;
            font-weight: 500;
            margin: 5px;
        }
        button:hover {
            background: #c06c6c;
        }
        button:disabled {
            background: #ccc;
            cursor: not-allowed;
        }
        .status {
            padding: 10px;
            border-radius: 5px;
            margin: 10px 0;
        }
        .success { background: #d4edda; color: #155724; border: 1px solid #c3e6cb; }
        .error { background: #f8d7da; color: #721c24; border: 1px solid #f5c6cb; }
        .info { background: #d1ecf1; color: #0c5460; border: 1px solid #bee5eb; }
        .users-list {
            max-height: 300px;
            overflow-y: auto;
            border: 1px solid #ddd;
            border-radius: 5px;
            padding: 10px;
        }
        .user-item {
            display: flex;
            align-items: center;
            padding: 10px;
            border-bottom: 1px solid #eee;
        }
        .user-item:last-child {
            border-bottom: none;
        }
        .user-avatar {
            width: 40px;
            height: 40px;
            border-radius: 50%;
            margin-right: 12px;
        }
        .user-info {
            flex: 1;
        }
        .user-name {
            font-weight: 500;
            color: #333;
        }
        .user-email {
            font-size: 12px;
            color: #666;
        }
        .user-type {
            background: #f0f0f0;
            padding: 2px 8px;
            border-radius: 12px;
            font-size: 11px;
            color: #666;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🔥 Firebase Authentication Demo</h1>
        <p>Create test users to test the chat functionality</p>

        <div id="auth-status" class="status info">
            Checking authentication status...
        </div>

        <div class="section">
            <h3>📝 Quick User Creation</h3>
            <p>Create test users quickly with predefined data:</p>
            
            <button onclick="createTestStudent()">Create Test Student</button>
            <button onclick="createTestEmployer()">Create Test Employer</button>
            <button onclick="createMultipleUsers()">Create 5 Test Users</button>
        </div>

        <div class="section">
            <h3>👤 Custom User Creation</h3>
            <form id="custom-user-form">
                <div class="form-group">
                    <label for="user-type">User Type:</label>
                    <select id="user-type" required>
                        <option value="student">Student</option>
                        <option value="employer">Employer</option>
                    </select>
                </div>
                
                <div class="form-group">
                    <label for="user-name">Full Name:</label>
                    <input type="text" id="user-name" required>
                </div>
                
                <div class="form-group">
                    <label for="user-email">Email:</label>
                    <input type="email" id="user-email" required>
                </div>
                
                <div class="form-group">
                    <label for="user-password">Password:</label>
                    <input type="password" id="user-password" value="password123" required>
                </div>
                
                <div id="student-fields" style="display: none;">
                    <div class="form-group">
                        <label for="university">University:</label>
                        <input type="text" id="university" value="Demo University">
                    </div>
                    
                    <div class="form-group">
                        <label for="year-of-study">Year of Study:</label>
                        <input type="number" id="year-of-study" value="3" min="1" max="6">
                    </div>
                    
                    <div class="form-group">
                        <label for="department">Department:</label>
                        <input type="text" id="department" value="Computer Science">
                    </div>
                </div>
                
                <div id="employer-fields" style="display: none;">
                    <div class="form-group">
                        <label for="company-type">Company Type:</label>
                        <select id="company-type">
                            <option value="law-firm">Law Firm</option>
                            <option value="corporate">Corporate</option>
                            <option value="government">Government</option>
                            <option value="non-profit">Non-Profit</option>
                        </select>
                    </div>
                    
                    <div class="form-group">
                        <label for="phone">Phone:</label>
                        <input type="tel" id="phone" value="+1234567890">
                    </div>
                </div>
                
                <button type="submit">Create Custom User</button>
            </form>
        </div>

        <div class="section">
            <h3>👥 Current Users</h3>
            <button onclick="loadUsers()">Refresh Users List</button>
            <button onclick="signOutCurrentUser()" style="background: #dc3545;">Sign Out Current User</button>
            
            <div id="users-list" class="users-list">
                <div style="text-align: center; padding: 20px; color: #666;">
                    Click "Refresh Users List" to load users
                </div>
            </div>
        </div>

        <div class="section">
            <h3>🚀 Test Chat</h3>
            <p>After creating users, test the chat functionality:</p>
            <button onclick="openChat()">Open Chat Interface</button>
            <button onclick="openFirebaseTest()">Open Firebase Test</button>
        </div>
    </div>

    <script type="module">
        import { initializeApp } from 'https://www.gstatic.com/firebasejs/9.22.0/firebase-app.js';
        import { 
            getAuth, 
            createUserWithEmailAndPassword,
            signOut,
            onAuthStateChanged,
            updateProfile
        } from 'https://www.gstatic.com/firebasejs/9.22.0/firebase-auth.js';
        import { 
            getFirestore, 
            doc, 
            setDoc,
            collection,
            getDocs
        } from 'https://www.gstatic.com/firebasejs/9.22.0/firebase-firestore.js';

        // Firebase configuration
        const firebaseConfig = {
            apiKey: "AIzaSyA2Is7jXaYL4k04tSaI-CRzmtisQ5VSmz4",
            authDomain: "shift-3140e.firebaseapp.com",
            databaseURL: "https://shift-3140e-default-rtdb.firebaseio.com",
            projectId: "shift-3140e",
            storageBucket: "shift-3140e.firebasestorage.app",
            messagingSenderId: "716245939154",
            appId: "1:716245939154:web:64d567a1ded3fa98b34e0b",
            measurementId: "G-F6WJ0T3E71"
        };

        // Initialize Firebase
        const app = initializeApp(firebaseConfig);
        const auth = getAuth(app);
        const db = getFirestore(app);

        let currentUser = null;

        // Auth state listener
        onAuthStateChanged(auth, (user) => {
            currentUser = user;
            updateAuthStatus(user);
        });

        function updateAuthStatus(user) {
            const statusDiv = document.getElementById('auth-status');
            if (user) {
                statusDiv.className = 'status success';
                statusDiv.innerHTML = `✅ Signed in as: ${user.email}`;
            } else {
                statusDiv.className = 'status info';
                statusDiv.innerHTML = '🔓 Not signed in (this is normal for creating test users)';
            }
        }

        // User type change handler
        document.getElementById('user-type').addEventListener('change', function() {
            const studentFields = document.getElementById('student-fields');
            const employerFields = document.getElementById('employer-fields');
            
            if (this.value === 'student') {
                studentFields.style.display = 'block';
                employerFields.style.display = 'none';
            } else {
                studentFields.style.display = 'none';
                employerFields.style.display = 'block';
            }
        });

        // Custom user form handler
        document.getElementById('custom-user-form').addEventListener('submit', async (e) => {
            e.preventDefault();
            
            const userType = document.getElementById('user-type').value;
            const userData = {
                name: document.getElementById('user-name').value,
                email: document.getElementById('user-email').value,
                password: document.getElementById('user-password').value,
                userType: userType
            };

            if (userType === 'student') {
                userData.university = document.getElementById('university').value;
                userData.yearOfStudy = parseInt(document.getElementById('year-of-study').value);
                userData.department = document.getElementById('department').value;
            } else {
                userData.companyType = document.getElementById('company-type').value;
                userData.phone = document.getElementById('phone').value;
            }

            await createUser(userData);
        });

        // Create user function
        async function createUser(userData) {
            try {
                showStatus('Creating user...', 'info');

                // Create Firebase user
                const userCredential = await createUserWithEmailAndPassword(
                    auth, 
                    userData.email, 
                    userData.password
                );

                // Update profile
                await updateProfile(userCredential.user, {
                    displayName: userData.name
                });

                // Create user document (only include defined fields)
                const userProfile = {
                    uid: userCredential.user.uid,
                    email: userData.email,
                    name: userData.name,
                    userType: userData.userType,
                    isOnline: false,
                    lastSeen: new Date(),
                    profilePicture: generateAvatarUrl(userData.name),
                    createdAt: new Date(),
                    updatedAt: new Date()
                };

                // Add student-specific fields only if user is a student
                if (userData.userType === 'student') {
                    if (userData.university) userProfile.university = userData.university;
                    if (userData.yearOfStudy) userProfile.yearOfStudy = userData.yearOfStudy;
                    if (userData.department) userProfile.department = userData.department;
                }

                // Add employer-specific fields only if user is an employer
                if (userData.userType === 'employer') {
                    if (userData.companyType) userProfile.companyType = userData.companyType;
                    if (userData.phone) userProfile.phone = userData.phone;
                }

                await setDoc(doc(db, 'users', userCredential.user.uid), userProfile);

                showStatus(`✅ User created successfully: ${userData.name} (${userData.email})`, 'success');
                
                // Clear form
                document.getElementById('custom-user-form').reset();
                
                // Refresh users list
                setTimeout(loadUsers, 1000);

            } catch (error) {
                console.error('Error creating user:', error);
                showStatus(`❌ Error creating user: ${error.message}`, 'error');
            }
        }

        // Generate avatar URL
        function generateAvatarUrl(name) {
            const initial = name ? name.charAt(0).toUpperCase() : 'U';
            const colors = ['d17e7e', '5a3e5d', '8c6c8e', '4a3249'];
            const colorIndex = name ? name.length % colors.length : 0;
            const color = colors[colorIndex];
            
            return `https://via.placeholder.com/100x100/${color}/ffffff?text=${initial}`;
        }

        // Show status message
        function showStatus(message, type) {
            const statusDiv = document.getElementById('auth-status');
            statusDiv.className = `status ${type}`;
            statusDiv.innerHTML = message;
        }

        // Test user creation functions
        window.createTestStudent = async () => {
            const userData = {
                name: `Student ${Date.now()}`,
                email: `student${Date.now()}@test.com`,
                password: 'password123',
                userType: 'student',
                university: 'Test University',
                yearOfStudy: 3,
                department: 'Computer Science'
            };
            await createUser(userData);
        };

        window.createTestEmployer = async () => {
            const userData = {
                name: `Employer ${Date.now()}`,
                email: `employer${Date.now()}@test.com`,
                password: 'password123',
                userType: 'employer',
                companyType: 'law-firm',
                phone: '+1234567890'
            };
            await createUser(userData);
        };

        window.createMultipleUsers = async () => {
            showStatus('Creating 5 test users...', 'info');
            
            const users = [
                { name: 'Alice Johnson', email: `alice${Date.now()}@test.com`, userType: 'student', university: 'Harvard Law', department: 'Law', yearOfStudy: 2 },
                { name: 'Bob Smith', email: `bob${Date.now()}@test.com`, userType: 'employer', companyType: 'law-firm', phone: '+1111111111' },
                { name: 'Carol Davis', email: `carol${Date.now()}@test.com`, userType: 'student', university: 'Yale Law', department: 'Law', yearOfStudy: 4 },
                { name: 'David Wilson', email: `david${Date.now()}@test.com`, userType: 'employer', companyType: 'corporate', phone: '+2222222222' },
                { name: 'Emma Brown', email: `emma${Date.now()}@test.com`, userType: 'student', university: 'Stanford Law', department: 'Law', yearOfStudy: 1 }
            ];

            for (const userData of users) {
                userData.password = 'password123';
                await createUser(userData);
                await new Promise(resolve => setTimeout(resolve, 500)); // Small delay
            }
        };

        // Load users function
        window.loadUsers = async () => {
            try {
                showStatus('Loading users...', 'info');
                
                const usersRef = collection(db, 'users');
                const snapshot = await getDocs(usersRef);
                
                const usersList = document.getElementById('users-list');
                usersList.innerHTML = '';
                
                if (snapshot.empty) {
                    usersList.innerHTML = `
                        <div style="text-align: center; padding: 20px; color: #666;">
                            No users found. Create some test users first.
                        </div>
                    `;
                    showStatus('No users found in database', 'info');
                    return;
                }
                
                snapshot.forEach((doc) => {
                    const user = doc.data();
                    const userElement = document.createElement('div');
                    userElement.className = 'user-item';
                    
                    userElement.innerHTML = `
                        <img src="${user.profilePicture}" alt="${user.name}" class="user-avatar">
                        <div class="user-info">
                            <div class="user-name">${user.name}</div>
                            <div class="user-email">${user.email}</div>
                        </div>
                        <div class="user-type">${user.userType}</div>
                    `;
                    
                    usersList.appendChild(userElement);
                });
                
                showStatus(`✅ Loaded ${snapshot.size} users`, 'success');
                
            } catch (error) {
                console.error('Error loading users:', error);
                showStatus(`❌ Error loading users: ${error.message}`, 'error');
            }
        };

        // Sign out current user
        window.signOutCurrentUser = async () => {
            try {
                await signOut(auth);
                showStatus('✅ Signed out successfully', 'success');
            } catch (error) {
                showStatus(`❌ Error signing out: ${error.message}`, 'error');
            }
        };

        // Navigation functions
        window.openChat = () => {
            window.open('chat.html', '_blank');
        };

        window.openFirebaseTest = () => {
            window.open('firebase-test.html', '_blank');
        };

        // Initialize
        document.getElementById('user-type').dispatchEvent(new Event('change'));
    </script>
</body>
</html>
