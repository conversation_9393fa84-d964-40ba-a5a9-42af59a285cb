// Firebase Rules Deployment Script
// This script will help you deploy Firebase rules to your project

import { initializeApp } from 'https://www.gstatic.com/firebasejs/9.22.0/firebase-app.js';
import { getFirestore } from 'https://www.gstatic.com/firebasejs/9.22.0/firebase-firestore.js';

const firebaseConfig = {
    apiKey: "AIzaSyA2Is7jXaYL4k04tSaI-CRzmtisQ5VSmz4",
    authDomain: "shift-3140e.firebaseapp.com",
    projectId: "shift-3140e",
    storageBucket: "shift-3140e.firebasestorage.app",
    messagingSenderId: "716245939154",
    appId: "1:716245939154:web:64d567a1ded3fa98b34e0b",
    measurementId: "G-F6WJ0T3E71"
};

// Initialize Firebase
const app = initializeApp(firebaseConfig);
const db = getFirestore(app);

console.log('Firebase initialized successfully!');
console.log('Project ID:', firebaseConfig.projectId);

// Instructions for deploying rules
console.log(`
🔥 FIREBASE RULES DEPLOYMENT INSTRUCTIONS:

1. Install Firebase CLI:
   npm install -g firebase-tools

2. Login to Firebase:
   firebase login

3. Initialize Firebase in your project:
   firebase init

4. Deploy the rules:
   firebase deploy --only firestore:rules

5. Check the console for deployment status

Your rules file (firestore.rules) has been created with development-friendly permissions.
Remember to implement proper security rules for production!
`);

export { db, app };
