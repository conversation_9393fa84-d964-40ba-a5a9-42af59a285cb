// Please paste your current Firebase rules here
// Copy them from Firebase Console > Firestore Database > Rules tab
// Then I can read them and help you fix the permission issue

rules_version = '2';
service cloud.firestore {
  match /databases/{database}/documents {
    // Jobs collection - anyone can read, only authenticated employers can write
    match /jobs/{document} {
      allow read: if true;  // Students can read all jobs
      allow write: if request.auth != null && 
                   request.auth.token.email != null &&
                   request.resource.data.employerEmail == request.auth.token.email;
    }
    
    // Applications collection - students can create, employers can read their jobs' applications
    match /applications/{document} {
      allow create: if request.auth != null && request.auth.token.email != null;
      allow read: if request.auth != null && 
                 (resource.data.studentEmail == request.auth.token.email || 
                  resource.data.employerEmail == request.auth.token.email);
      allow update: if request.auth != null && 
                   resource.data.employerEmail == request.auth.token.email;
    }
    
    // Users collection - users can read/write their own data
    match /users/{userId} {
      allow read, write: if request.auth != null && request.auth.uid == userId;
    }
    
    // Chat collections - only authenticated users
    match /conversations/{document} {
      allow read, write: if request.auth != null;
    }
    
    match /messages/{document} {
      allow read, write: if request.auth != null;
    }
  }
}
