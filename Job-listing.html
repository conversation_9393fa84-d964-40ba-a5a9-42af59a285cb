<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Job Listing - Legal Career Platform</title>
    <link rel="stylesheet" href="job-listing.css">
    <link rel="stylesheet" href="modern-enhancements.css">
</head>
<body>
    <div class="container">
        <div class="header">
            <div class="logo">S</div>
            <div class="user-info" id="userInfo">
                <span class="welcome-text">Welcome, <span id="userName">Student</span></span>
                <span class="year-badge" id="yearBadge">Year 2</span>
            </div>
        </div>
        
        <div class="main-content">
            <h1 class="title">Job Listing</h1>
            
            <div class="search-section">
                <div class="search-container">
                    <input type="text" class="search-input" placeholder="Search jobs..." id="searchInput">
                    <button class="search-btn" id="searchBtn">
                        <span class="search-icon">🔍</span>
                    </button>
                </div>
                <!-- Removed filter button -->
            </div>
            
            <!-- Dynamic jobs grid will be rendered here -->
            <div id="jobsGrid" class="job-categories-grid"></div>
            <div id="noJobsMessage" style="display:none;"></div>
        </div>
        
        <div class="bottom-nav">
            <a href="dashboard.html" class="nav-item active">
                <svg width="24" height="24" viewBox="0 0 24 24" fill="none">
                    <path d="M3 9L12 2L21 9V20C21 20.5304 20.7893 21.0391 20.4142 21.4142C20.0391 21.7893 19.5304 22 19 22H5C4.46957 22 3.96086 21.7893 3.58579 21.4142C3.21071 21.0391 3 20.5304 3 20V9Z" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
                    <path d="M9 22V12H15V22" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
                </svg>
                <span>Home</span>
            </a>
            <a href="search.html" class="nav-item">
                <svg width="24" height="24" viewBox="0 0 24 24" fill="none">
                    <circle cx="11" cy="11" r="8" stroke="currentColor" stroke-width="2"/>
                    <path d="M21 21L16.65 16.65" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
                </svg>
                <span>Search</span>
            </a>
            <a href="chat.html" class="nav-item">
                <svg width="24" height="24" viewBox="0 0 24 24" fill="none">
                    <path d="M21 15C21 15.5304 20.7893 16.0391 20.4142 16.4142C20.0391 16.7893 19.5304 17 19 17H7L3 21V5C3 4.46957 3.21071 3.96086 3.58579 3.58579C3.96086 3.21071 4.46957 3 5 3H19C19.5304 3 20.0391 3.21071 20.4142 3.58579C20.7893 3.96086 21 4.46957 21 5V15Z" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
                </svg>
                <span>Messages</span>
            </a>
            <a href="student-profile.html" class="nav-item">
                <svg width="24" height="24" viewBox="0 0 24 24" fill="none">
                    <path d="M20 21V19C20 17.9391 19.5786 16.9217 18.8284 16.1716C18.0783 15.4214 17.0609 15 16 15H8C6.93913 15 5.92172 15.4214 5.17157 16.1716C4.42143 16.9217 4 17.9391 4 19V21" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
                    <circle cx="12" cy="7" r="4" stroke="currentColor" stroke-width="2"/>
                </svg>
                <span>Profile</span>
            </a>
            <!-- Notifications nav item -->
            <a href="#" class="nav-item" id="studentNotificationsNav">
                <svg width="24" height="24" viewBox="0 0 24 24" fill="none">
                    <path d="M18 8A6 6 0 0 0 6 8c0 7-3 9-3 9h18s-3-2-3-9" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
                    <path d="M13.73 21a2 2 0 0 1-3.46 0" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
                </svg>
                <span>Notifications</span>
            </a>
        </div>
        <div id="studentNotificationsModal" style="display:none;position:fixed;top:0;left:0;width:100vw;height:100vh;background:rgba(0,0,0,0.5);z-index:9999;align-items:center;justify-content:center;"></div>
    </div>

    <script type="module">
  import { getAuth, onAuthStateChanged } from 'https://www.gstatic.com/firebasejs/9.22.0/firebase-auth.js';
  import { initializeApp } from 'https://www.gstatic.com/firebasejs/9.22.0/firebase-app.js';

  const firebaseConfig = {
    apiKey: "AIzaSyA2Is7jXaYL4k04tSaI-CRzmtisQ5VSmz4",
    authDomain: "shift-3140e.firebaseapp.com",
    projectId: "shift-3140e",
    storageBucket: "shift-3140e.firebasestorage.app",
    messagingSenderId: "716245939154",
    appId: "1:716245939154:web:64d567a1ded3fa98b34e0b",
    measurementId: "G-F6WJ0T3E71"
  };

  if (!window.firebaseApp) {
    window.firebaseApp = initializeApp(firebaseConfig);
  }
  const auth = getAuth();

  onAuthStateChanged(auth, (user) => {
    if (!user) {
      window.location.href = 'studentlogin.html';
    }
  });
</script>
    <script>
        function selectJobCategory(category) {
            // Store the selected category
            localStorage.setItem('selectedJobCategory', category);

            // Get category details
            const categoryDetails = {
                'legal-filings': {
                    title: 'Preparing Legal Filings & Case Documents',
                    description: 'Create and prepare legal documents, filings, and case materials'
                },
                'document-summary': {
                    title: 'Summarizing Legal Documents',
                    description: 'Review and summarize complex legal documents and contracts'
                },
                'corporate-advisory': {
                    title: 'Corporate Legal Advisory Support',
                    description: 'Provide legal advisory support for corporate matters and compliance'
                },
                'legal-research': {
                    title: 'Legal Research Assistance',
                    description: 'Conduct thorough legal research and analysis on various topics'
                }
            };

            const selected = categoryDetails[category];

            // Show job details or redirect to job application
            alert(`You selected: ${selected.title}\n\nDescription: ${selected.description}\n\nThis would typically show available jobs in this category or redirect to application.`);
        }
    </script>
    <script src="job-listing.js"></script>
    <!-- Chat Integration -->
    <script src="chat-integration-widget.js"></script>
    <script src="chat-buttons.js"></script>
    <!-- Authentication Router -->
    <script type="module" src="auth-router.js"></script>
</body>
</html>