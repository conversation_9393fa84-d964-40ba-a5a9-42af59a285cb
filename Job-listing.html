<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Job Listing - Legal Career Platform</title>
    <link rel="stylesheet" href="job-listing.css">
</head>
<body>
    <div class="container">
        <div class="header">
            <div class="logo">S</div>
            <div class="user-info" id="userInfo">
                <span class="welcome-text">Welcome, <span id="userName">Student</span></span>
                <span class="year-badge" id="yearBadge">Year 2</span>
            </div>
        </div>
        
        <div class="main-content">
            <h1 class="title">Job Listing</h1>
            
            <div class="search-section">
                <div class="search-container">
                    <input type="text" class="search-input" placeholder="Search jobs..." id="searchInput">
                    <button class="search-btn" id="searchBtn">
                        <span class="search-icon">🔍</span>
                    </button>
                </div>
                <!-- Removed filter button -->
            </div>
            
            <!-- Dynamic jobs grid will be rendered here -->
            <div id="jobsGrid" class="job-categories-grid"></div>
            <div id="noJobsMessage" style="display:none;"></div>
        </div>
        
        <div class="bottom-nav">
            <a href="dashboard.html" class="nav-item active">
                <svg width="24" height="24" viewBox="0 0 24 24" fill="none">
                    <path d="M3 9L12 2L21 9V20C21 20.5304 20.7893 21.0391 20.4142 21.4142C20.0391 21.7893 19.5304 22 19 22H5C4.46957 22 3.96086 21.7893 3.58579 21.4142C3.21071 21.0391 3 20.5304 3 20V9Z" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
                    <path d="M9 22V12H15V22" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
                </svg>
                <span>Home</span>
            </a>
            <a href="search.html" class="nav-item">
                <svg width="24" height="24" viewBox="0 0 24 24" fill="none">
                    <circle cx="11" cy="11" r="8" stroke="currentColor" stroke-width="2"/>
                    <path d="M21 21L16.65 16.65" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
                </svg>
                <span>Search</span>
            </a>
            <a href="chat.html" class="nav-item">
                <svg width="24" height="24" viewBox="0 0 24 24" fill="none">
                    <path d="M21 15C21 15.5304 20.7893 16.0391 20.4142 16.4142C20.0391 16.7893 19.5304 17 19 17H7L3 21V5C3 4.46957 3.21071 3.96086 3.58579 3.58579C3.96086 3.21071 4.46957 3 5 3H19C19.5304 3 20.0391 3.21071 20.4142 3.58579C20.7893 3.96086 21 4.46957 21 5V15Z" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
                </svg>
                <span>Messages</span>
            </a>
            <a href="student-profile.html" class="nav-item">
                <svg width="24" height="24" viewBox="0 0 24 24" fill="none">
                    <path d="M20 21V19C20 17.9391 19.5786 16.9217 18.8284 16.1716C18.0783 15.4214 17.0609 15 16 15H8C6.93913 15 5.92172 15.4214 5.17157 16.1716C4.42143 16.9217 4 17.9391 4 19V21" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
                    <circle cx="12" cy="7" r="4" stroke="currentColor" stroke-width="2"/>
                </svg>
                <span>Profile</span>
            </a>
            <!-- Notifications nav item -->
            <a href="#" class="nav-item" id="studentNotificationsNav">
                <svg width="24" height="24" viewBox="0 0 24 24" fill="none">
                    <path d="M18 8A6 6 0 0 0 6 8c0 7-3 9-3 9h18s-3-2-3-9" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
                    <path d="M13.73 21a2 2 0 0 1-3.46 0" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
                </svg>
                <span>Notifications</span>
            </a>
        </div>
        <div id="studentNotificationsModal" style="display:none;position:fixed;top:0;left:0;width:100vw;height:100vh;background:rgba(0,0,0,0.5);z-index:9999;align-items:center;justify-content:center;"></div>
    </div>

    <script type="module">
  import { getAuth, onAuthStateChanged } from 'https://www.gstatic.com/firebasejs/9.22.0/firebase-auth.js';
  import { initializeApp } from 'https://www.gstatic.com/firebasejs/9.22.0/firebase-app.js';

  const firebaseConfig = {
    apiKey: "AIzaSyA2Is7jXaYL4k04tSaI-CRzmtisQ5VSmz4",
    authDomain: "shift-3140e.firebaseapp.com",
    projectId: "shift-3140e",
    storageBucket: "shift-3140e.firebasestorage.app",
    messagingSenderId: "716245939154",
    appId: "1:716245939154:web:64d567a1ded3fa98b34e0b",
    measurementId: "G-F6WJ0T3E71"
  };

  if (!window.firebaseApp) {
    window.firebaseApp = initializeApp(firebaseConfig);
  }
  const auth = getAuth();

  onAuthStateChanged(auth, (user) => {
    if (!user) {
      window.location.href = 'studentlogin.html';
    }
  });
</script>
    <script>
        async function selectJobCategory(category) {
            // Store the selected category
            localStorage.setItem('selectedJobCategory', category);

            // Show loading message
            const loadingModal = document.createElement('div');
            loadingModal.style.cssText = `
                position: fixed;
                top: 0;
                left: 0;
                width: 100%;
                height: 100%;
                background: rgba(0, 0, 0, 0.5);
                display: flex;
                justify-content: center;
                align-items: center;
                z-index: 1000;
            `;
            loadingModal.innerHTML = `
                <div style="background: white; padding: 30px; border-radius: 10px; text-align: center;">
                    <h3>🔍 Loading Jobs...</h3>
                    <p>Fetching jobs from database...</p>
                </div>
            `;
            document.body.appendChild(loadingModal);

            try {
                // Get jobs from Firebase
                console.log('🔍 Fetching jobs from Firebase for category:', category);
                const firebaseJobs = await window.jobManager.getJobsByCategory(category);

                // Also get localStorage jobs as fallback
                const localJobs = JSON.parse(localStorage.getItem('jobs') || '[]');
                const localCategoryJobs = localJobs.filter(job => job.category === category);

                // Combine Firebase and local jobs (remove duplicates)
                const allJobs = [...firebaseJobs];
                localCategoryJobs.forEach(localJob => {
                    if (!firebaseJobs.find(fbJob => fbJob.title === localJob.title && fbJob.employerEmail === localJob.employerEmail)) {
                        allJobs.push(localJob);
                    }
                });

                console.log(`✅ Found ${firebaseJobs.length} Firebase jobs + ${localCategoryJobs.length} local jobs = ${allJobs.length} total`);

                // Remove loading modal
                loadingModal.remove();

                if (allJobs.length === 0) {
                    alert(`❌ No jobs found for category: ${category}\n\nThis could mean:\n• No employers have published jobs in this category yet\n• Network connection issues\n• Database sync problems\n\nTry refreshing the page or check back later.`);
                    return;
                }

                // Show jobs in this category
                showCategoryJobs(category, allJobs);

            } catch (error) {
                console.error('❌ Error fetching jobs:', error);
                loadingModal.remove();

                // Fallback to localStorage
                const localJobs = JSON.parse(localStorage.getItem('jobs') || '[]');
                const categoryJobs = localJobs.filter(job => job.category === category);

                if (categoryJobs.length > 0) {
                    alert(`⚠️ Network error, showing local jobs only.\n\nFound ${categoryJobs.length} local jobs.`);
                    showCategoryJobs(category, categoryJobs);
                } else {
                    alert(`❌ Error loading jobs: ${error.message}\n\nNo local jobs available either.`);
                }
            }
        }

        function showCategoryJobs(category, jobs, debugMode = false) {
            // Create modal to show jobs
            const modal = document.createElement('div');
            modal.style.cssText = `
                position: fixed;
                top: 0;
                left: 0;
                width: 100%;
                height: 100%;
                background: rgba(0, 0, 0, 0.5);
                display: flex;
                justify-content: center;
                align-items: center;
                z-index: 1000;
            `;

            const categoryTitles = {
                'legal-filings': 'Preparing Legal Filings & Case Documents',
                'document-summary': 'Summarizing Legal Documents',
                'corporate-advisory': 'Corporate Legal Advisory Support',
                'legal-research': 'Legal Research Assistance'
            };

            let jobsHTML = '';
            jobs.forEach((job, index) => {
                jobsHTML += `
                    <div class="job-item" style="border: 1px solid #ddd; padding: 15px; margin: 10px 0; border-radius: 8px; background: white;">
                        <h3>${job.title}</h3>
                        <p><strong>Employer:</strong> ${job.employerName || 'Unknown Employer'}</p>
                        <p><strong>Description:</strong> ${job.description}</p>
                        <p><strong>Payment:</strong> ${job.paymentCode}</p>
                        <p><strong>Expected Hours:</strong> ${job.expectedHours}</p>
                        ${debugMode ? `<p><strong>Category:</strong> ${job.category}</p>` : ''}
                        ${debugMode ? `<p><strong>Published:</strong> ${new Date(job.timestamp).toLocaleString()}</p>` : ''}
                        <button onclick="applyToJob(${index}, '${category}')" style="background: #d17e7e; color: white; border: none; padding: 10px 20px; border-radius: 5px; cursor: pointer;">
                            Apply Now
                        </button>
                    </div>
                `;
            });

            const modalTitle = debugMode ? `All Jobs (Debug Mode)` : categoryTitles[category];

            modal.innerHTML = `
                <div style="background: white; padding: 30px; border-radius: 10px; max-width: 600px; max-height: 80vh; overflow-y: auto;">
                    <div style="display: flex; justify-content: space-between; align-items: center; margin-bottom: 20px;">
                        <h2>${modalTitle}</h2>
                        <button onclick="this.parentElement.parentElement.parentElement.remove()" style="background: none; border: none; font-size: 24px; cursor: pointer;">&times;</button>
                    </div>
                    <div>
                        ${jobsHTML}
                    </div>
                </div>
            `;

            document.body.appendChild(modal);

            // Store jobs for application
            window.currentCategoryJobs = jobs;
        }

        function applyToJob(jobIndex, category) {
            const job = window.currentCategoryJobs[jobIndex];
            const currentUser = JSON.parse(localStorage.getItem('currentUser') || '{}');

            if (!currentUser.name) {
                alert('Please sign in to apply for jobs');
                return;
            }

            // Create application
            const application = {
                jobTitle: job.title,
                jobCategory: category,
                employerName: job.employerName,
                employerEmail: job.employerEmail,
                studentName: currentUser.name,
                studentEmail: currentUser.email,
                studentYear: currentUser.yearOfStudy,
                studentUniversity: currentUser.university,
                studentDepartment: currentUser.department,
                appliedAt: new Date().toISOString(),
                status: 'Applied'
            };

            // Save application
            const applications = JSON.parse(localStorage.getItem('jobApplications') || '[]');
            applications.push(application);
            localStorage.setItem('jobApplications', JSON.stringify(applications));

            alert(`Application submitted successfully!\n\nJob: ${job.title}\nEmployer: ${job.employerName}\n\nYour application has been sent to the employer.`);

            // Close modal
            document.querySelector('[style*="position: fixed"]').remove();
        }
    </script>
    <script src="job-listing.js"></script>
    <!-- Firebase Jobs Management -->
    <script type="module" src="firebase-jobs.js"></script>
    <!-- Chat Integration -->
    <script src="chat-integration-widget.js"></script>
    <script src="chat-buttons.js"></script>
    <!-- Authentication Router -->
    <script type="module" src="auth-router.js"></script>
</body>
</html>