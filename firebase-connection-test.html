<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Firebase Connection Test</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 600px;
            margin: 50px auto;
            padding: 20px;
            background: #f5f5f5;
        }
        .container {
            background: white;
            padding: 30px;
            border-radius: 10px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        .status {
            padding: 15px;
            border-radius: 5px;
            margin: 10px 0;
            font-weight: bold;
        }
        .success { background: #d4edda; color: #155724; }
        .error { background: #f8d7da; color: #721c24; }
        .info { background: #d1ecf1; color: #0c5460; }
        .loading { background: #fff3cd; color: #856404; }
        button {
            background: #007bff;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 5px;
            cursor: pointer;
            margin: 5px;
        }
        button:hover { background: #0056b3; }
        .test-section {
            margin: 20px 0;
            padding: 15px;
            border: 1px solid #ddd;
            border-radius: 5px;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🔥 Firebase Connection Test</h1>
        
        <div class="test-section">
            <h3>1. Firebase SDK Loading</h3>
            <div id="sdk-status" class="status loading">Testing SDK loading...</div>
        </div>

        <div class="test-section">
            <h3>2. Firebase App Initialization</h3>
            <div id="app-status" class="status loading">Testing app initialization...</div>
        </div>

        <div class="test-section">
            <h3>3. Firestore Connection</h3>
            <div id="firestore-status" class="status loading">Testing Firestore connection...</div>
            <button onclick="testFirestore()">Test Firestore</button>
        </div>

        <div class="test-section">
            <h3>4. Authentication Service</h3>
            <div id="auth-status" class="status loading">Testing Auth service...</div>
            <button onclick="testAuth()">Test Auth</button>
        </div>

        <div class="test-section">
            <h3>5. Network Connectivity</h3>
            <div id="network-status" class="status loading">Testing network...</div>
            <button onclick="testNetwork()">Test Network</button>
        </div>

        <div class="test-section">
            <h3>6. Simple User Creation Test</h3>
            <div id="user-test-status" class="status info">Ready to test</div>
            <button onclick="testUserCreation()">Test Create User</button>
        </div>

        <div class="test-section">
            <h3>Debug Information</h3>
            <div id="debug-info" style="font-family: monospace; font-size: 12px; background: #f8f9fa; padding: 10px; border-radius: 4px;">
                Debug info will appear here...
            </div>
        </div>
    </div>

    <script type="module">
        let app, db, auth;
        
        // Firebase configuration
        const firebaseConfig = {
            apiKey: "AIzaSyA2Is7jXaYL4k04tSaI-CRzmtisQ5VSmz4",
            authDomain: "shift-3140e.firebaseapp.com",
            databaseURL: "https://shift-3140e-default-rtdb.firebaseio.com",
            projectId: "shift-3140e",
            storageBucket: "shift-3140e.firebasestorage.app",
            messagingSenderId: "716245939154",
            appId: "1:716245939154:web:64d567a1ded3fa98b34e0b",
            measurementId: "G-F6WJ0T3E71"
        };

        function updateStatus(elementId, message, type) {
            const element = document.getElementById(elementId);
            element.textContent = message;
            element.className = `status ${type}`;
        }

        function addDebugInfo(message) {
            const debugElement = document.getElementById('debug-info');
            const timestamp = new Date().toLocaleTimeString();
            debugElement.innerHTML += `<div>[${timestamp}] ${message}</div>`;
        }

        // Test 1: SDK Loading
        try {
            const { initializeApp } = await import('https://www.gstatic.com/firebasejs/9.22.0/firebase-app.js');
            updateStatus('sdk-status', '✅ Firebase SDK loaded successfully', 'success');
            addDebugInfo('Firebase SDK imported successfully');
            
            // Test 2: App Initialization
            try {
                app = initializeApp(firebaseConfig);
                updateStatus('app-status', '✅ Firebase app initialized successfully', 'success');
                addDebugInfo('Firebase app initialized with config: ' + JSON.stringify(firebaseConfig, null, 2));
            } catch (error) {
                updateStatus('app-status', '❌ App initialization failed: ' + error.message, 'error');
                addDebugInfo('App initialization error: ' + error.message);
            }
            
        } catch (error) {
            updateStatus('sdk-status', '❌ SDK loading failed: ' + error.message, 'error');
            addDebugInfo('SDK loading error: ' + error.message);
        }

        // Test Firestore
        window.testFirestore = async function() {
            try {
                updateStatus('firestore-status', '🔄 Testing Firestore...', 'loading');
                
                const { getFirestore, doc, getDoc } = await import('https://www.gstatic.com/firebasejs/9.22.0/firebase-firestore.js');
                db = getFirestore(app);
                
                // Try to read a document (this tests connectivity)
                const testDoc = doc(db, 'test', 'connection');
                await getDoc(testDoc);
                
                updateStatus('firestore-status', '✅ Firestore connection successful', 'success');
                addDebugInfo('Firestore connection test passed');
                
            } catch (error) {
                updateStatus('firestore-status', '❌ Firestore failed: ' + error.message, 'error');
                addDebugInfo('Firestore error: ' + error.message);
                addDebugInfo('Error code: ' + error.code);
            }
        };

        // Test Auth
        window.testAuth = async function() {
            try {
                updateStatus('auth-status', '🔄 Testing Auth...', 'loading');
                
                const { getAuth } = await import('https://www.gstatic.com/firebasejs/9.22.0/firebase-auth.js');
                auth = getAuth(app);
                
                updateStatus('auth-status', '✅ Auth service initialized', 'success');
                addDebugInfo('Auth service test passed');
                addDebugInfo('Auth instance: ' + (auth ? 'Created' : 'Failed'));
                
            } catch (error) {
                updateStatus('auth-status', '❌ Auth failed: ' + error.message, 'error');
                addDebugInfo('Auth error: ' + error.message);
            }
        };

        // Test Network
        window.testNetwork = async function() {
            try {
                updateStatus('network-status', '🔄 Testing network...', 'loading');
                
                // Test general internet connectivity
                const response = await fetch('https://www.google.com/favicon.ico', { mode: 'no-cors' });
                addDebugInfo('General internet: OK');
                
                // Test Firebase endpoints
                const firebaseTest = await fetch('https://firebase.googleapis.com/', { mode: 'no-cors' });
                addDebugInfo('Firebase endpoints: OK');
                
                updateStatus('network-status', '✅ Network connectivity good', 'success');
                
            } catch (error) {
                updateStatus('network-status', '❌ Network issues: ' + error.message, 'error');
                addDebugInfo('Network error: ' + error.message);
            }
        };

        // Test User Creation
        window.testUserCreation = async function() {
            try {
                updateStatus('user-test-status', '🔄 Testing user creation...', 'loading');
                
                if (!auth) {
                    throw new Error('Auth not initialized. Run Auth test first.');
                }
                
                const { createUserWithEmailAndPassword } = await import('https://www.gstatic.com/firebasejs/9.22.0/firebase-auth.js');
                
                const testEmail = `test${Date.now()}@example.com`;
                const testPassword = 'testpassword123';
                
                addDebugInfo(`Attempting to create user: ${testEmail}`);
                
                const userCredential = await createUserWithEmailAndPassword(auth, testEmail, testPassword);
                
                updateStatus('user-test-status', '✅ User creation successful!', 'success');
                addDebugInfo('User created successfully: ' + userCredential.user.email);
                addDebugInfo('User UID: ' + userCredential.user.uid);
                
            } catch (error) {
                updateStatus('user-test-status', '❌ User creation failed: ' + error.message, 'error');
                addDebugInfo('User creation error: ' + error.message);
                addDebugInfo('Error code: ' + error.code);
                
                if (error.code === 'auth/network-request-failed') {
                    addDebugInfo('NETWORK ISSUE DETECTED:');
                    addDebugInfo('- Check your internet connection');
                    addDebugInfo('- Check if firewall is blocking Firebase');
                    addDebugInfo('- Try using a different network (mobile hotspot)');
                }
            }
        };

        // Auto-run initial tests
        setTimeout(() => {
            testFirestore();
            testAuth();
            testNetwork();
        }, 1000);

    </script>
</body>
</html>
