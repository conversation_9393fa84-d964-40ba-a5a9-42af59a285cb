{"indexes": [{"collectionGroup": "jobs", "queryScope": "COLLECTION", "fields": [{"fieldPath": "category", "order": "ASCENDING"}, {"fieldPath": "status", "order": "ASCENDING"}, {"fieldPath": "createdAt", "order": "DESCENDING"}]}, {"collectionGroup": "jobs", "queryScope": "COLLECTION", "fields": [{"fieldPath": "employerEmail", "order": "ASCENDING"}, {"fieldPath": "createdAt", "order": "DESCENDING"}]}, {"collectionGroup": "applications", "queryScope": "COLLECTION", "fields": [{"fieldPath": "employerEmail", "order": "ASCENDING"}, {"fieldPath": "createdAt", "order": "DESCENDING"}]}, {"collectionGroup": "conversations", "queryScope": "COLLECTION", "fields": [{"fieldPath": "participants", "arrayConfig": "CONTAINS"}, {"fieldPath": "lastMessageTime", "order": "DESCENDING"}]}], "fieldOverrides": []}