<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Test Conversation Creation</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 600px;
            margin: 50px auto;
            padding: 20px;
        }
        .status {
            padding: 15px;
            border-radius: 5px;
            margin: 10px 0;
        }
        .success { background: #d4edda; color: #155724; }
        .error { background: #f8d7da; color: #721c24; }
        .info { background: #d1ecf1; color: #0c5460; }
        button {
            background: #007bff;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 5px;
            cursor: pointer;
            margin: 5px;
        }
    </style>
</head>
<body>
    <h1>🧪 Test Conversation Creation</h1>
    
    <div id="auth-status" class="status info">Checking authentication...</div>
    
    <button onclick="testConversationCreation()">Test Create Conversation</button>
    <button onclick="testPermissions()">Test Permissions</button>
    
    <div id="test-results"></div>

    <script type="module">
        import { initializeApp } from 'https://www.gstatic.com/firebasejs/9.22.0/firebase-app.js';
        import { 
            getAuth, 
            onAuthStateChanged,
            signInWithEmailAndPassword 
        } from 'https://www.gstatic.com/firebasejs/9.22.0/firebase-auth.js';
        import { 
            getFirestore, 
            doc, 
            setDoc, 
            getDoc,
            serverTimestamp 
        } from 'https://www.gstatic.com/firebasejs/9.22.0/firebase-firestore.js';

        // Firebase configuration
        const firebaseConfig = {
            apiKey: "AIzaSyA2Is7jXaYL4k04tSaI-CRzmtisQ5VSmz4",
            authDomain: "shift-3140e.firebaseapp.com",
            databaseURL: "https://shift-3140e-default-rtdb.firebaseio.com",
            projectId: "shift-3140e",
            storageBucket: "shift-3140e.firebasestorage.app",
            messagingSenderId: "716245939154",
            appId: "1:716245939154:web:64d567a1ded3fa98b34e0b",
            measurementId: "G-F6WJ0T3E71"
        };

        const app = initializeApp(firebaseConfig);
        const auth = getAuth(app);
        const db = getFirestore(app);

        let currentUser = null;

        function updateStatus(message, type) {
            document.getElementById('auth-status').innerHTML = message;
            document.getElementById('auth-status').className = `status ${type}`;
        }

        function addResult(message, type) {
            const resultsDiv = document.getElementById('test-results');
            const resultDiv = document.createElement('div');
            resultDiv.className = `status ${type}`;
            resultDiv.innerHTML = message;
            resultsDiv.appendChild(resultDiv);
        }

        // Check auth state
        onAuthStateChanged(auth, (user) => {
            currentUser = user;
            if (user) {
                updateStatus(`✅ Authenticated as: ${user.email}`, 'success');
            } else {
                updateStatus('❌ Not authenticated. Please sign in first.', 'error');
            }
        });

        window.testConversationCreation = async function() {
            if (!currentUser) {
                addResult('❌ Please sign in first', 'error');
                return;
            }

            try {
                addResult('🔄 Testing conversation creation...', 'info');

                const testUserId = 'test-user-123';
                const participants = [currentUser.uid, testUserId].sort();
                const conversationId = participants.join('_');

                const conversationRef = doc(db, 'conversations', conversationId);
                
                await setDoc(conversationRef, {
                    id: conversationId,
                    participants: participants,
                    createdAt: serverTimestamp(),
                    updatedAt: serverTimestamp(),
                    lastMessage: null,
                    lastMessageTime: null,
                    unreadCount: {}
                });

                addResult('✅ Conversation created successfully!', 'success');
                addResult(`Conversation ID: ${conversationId}`, 'info');

            } catch (error) {
                addResult(`❌ Conversation creation failed: ${error.message}`, 'error');
                addResult(`Error code: ${error.code}`, 'error');
                
                if (error.code === 'permission-denied') {
                    addResult('🔧 Fix: Update Firestore security rules to allow conversation creation', 'info');
                }
            }
        };

        window.testPermissions = async function() {
            if (!currentUser) {
                addResult('❌ Please sign in first', 'error');
                return;
            }

            try {
                addResult('🔄 Testing Firestore permissions...', 'info');

                // Test reading users collection
                const userRef = doc(db, 'users', currentUser.uid);
                const userSnap = await getDoc(userRef);
                
                if (userSnap.exists()) {
                    addResult('✅ Can read user document', 'success');
                } else {
                    addResult('⚠️ User document does not exist', 'info');
                }

                // Test writing to users collection
                await setDoc(userRef, {
                    lastTestAccess: new Date()
                }, { merge: true });
                
                addResult('✅ Can write to user document', 'success');

            } catch (error) {
                addResult(`❌ Permission test failed: ${error.message}`, 'error');
                addResult(`Error code: ${error.code}`, 'error');
            }
        };

        // Auto-sign in with existing user if available
        const userData = JSON.parse(localStorage.getItem('currentUser') || '{}');
        if (userData.email && !currentUser) {
            // Try to sign in with stored credentials (this is just for testing)
            addResult('🔄 Attempting to use stored user data...', 'info');
        }
    </script>
</body>
</html>
