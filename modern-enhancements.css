/* Modern Design Enhancements - Apply to all pages */

/* Enhanced Animations */
@keyframes fadeInUp {
    from {
        opacity: 0;
        transform: translateY(30px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

@keyframes slideInRight {
    from {
        opacity: 0;
        transform: translateX(30px);
    }
    to {
        opacity: 1;
        transform: translateX(0);
    }
}

@keyframes pulse {
    0%, 100% {
        transform: scale(1);
    }
    50% {
        transform: scale(1.05);
    }
}

@keyframes shimmer {
    0% {
        background-position: -200px 0;
    }
    100% {
        background-position: calc(200px + 100%) 0;
    }
}

/* Modern Button Enhancements */
.btn, button, input[type="submit"] {
    position: relative;
    overflow: hidden;
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

.btn::before, button::before, input[type="submit"]::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
    transition: left 0.5s;
}

.btn:hover::before, button:hover::before, input[type="submit"]:hover::before {
    left: 100%;
}

/* Modern Card Enhancements */
.card, .job-card, .signup-card, .conversation-item {
    position: relative;
    transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
}

.card:hover, .job-card:hover, .signup-card:hover {
    transform: translateY(-8px) scale(1.02);
}

/* Modern Input Enhancements */
.form-group input, .form-group textarea, .form-group select {
    transition: all 0.3s ease;
    border: 2px solid rgba(209, 126, 126, 0.2);
    background: rgba(255, 255, 255, 0.9);
    backdrop-filter: blur(10px);
}

.form-group input:focus, .form-group textarea:focus, .form-group select:focus {
    border-color: var(--color-primary);
    box-shadow: 0 0 0 3px rgba(209, 126, 126, 0.1);
    transform: translateY(-2px);
}

/* Modern Navigation Enhancements */
.nav-item, .navigation a {
    position: relative;
    transition: all 0.3s ease;
}

.nav-item::after, .navigation a::after {
    content: '';
    position: absolute;
    bottom: 0;
    left: 50%;
    width: 0;
    height: 2px;
    background: var(--color-primary);
    transition: all 0.3s ease;
    transform: translateX(-50%);
}

.nav-item:hover::after, .navigation a:hover::after {
    width: 100%;
}

/* Modern Loading States */
.loading-shimmer {
    background: linear-gradient(90deg, #f0f0f0 25%, #e0e0e0 50%, #f0f0f0 75%);
    background-size: 200px 100%;
    animation: shimmer 1.5s infinite;
}

/* Modern Scroll Enhancements */
::-webkit-scrollbar {
    width: 8px;
}

::-webkit-scrollbar-track {
    background: rgba(0, 0, 0, 0.05);
    border-radius: 4px;
}

::-webkit-scrollbar-thumb {
    background: rgba(209, 126, 126, 0.3);
    border-radius: 4px;
    transition: background 0.3s ease;
}

::-webkit-scrollbar-thumb:hover {
    background: rgba(209, 126, 126, 0.5);
}

/* Modern Glass Effects */
.glass-effect {
    background: rgba(255, 255, 255, 0.25);
    backdrop-filter: blur(20px);
    border: 1px solid rgba(255, 255, 255, 0.18);
    box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
}

/* Modern Gradient Text */
.gradient-text {
    background: linear-gradient(135deg, var(--color-primary) 0%, var(--color-secondary) 100%);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
}

/* Modern Floating Elements */
.floating {
    animation: float 3s ease-in-out infinite;
}

@keyframes float {
    0%, 100% {
        transform: translateY(0px);
    }
    50% {
        transform: translateY(-10px);
    }
}

/* Modern Focus States */
*:focus {
    outline: none;
    box-shadow: 0 0 0 3px rgba(209, 126, 126, 0.2);
}

/* Modern Typography Enhancements */
h1, h2, h3, h4, h5, h6 {
    font-weight: 600;
    letter-spacing: -0.025em;
}

/* Modern Spacing */
.section {
    padding: 4rem 0;
}

.container {
    padding: 0 1.5rem;
}

/* Modern Responsive Enhancements */
@media (max-width: 768px) {
    .card:hover, .job-card:hover, .signup-card:hover {
        transform: translateY(-4px) scale(1.01);
    }
    
    .section {
        padding: 2rem 0;
    }
}

/* Modern Dark Mode Support */
@media (prefers-color-scheme: dark) {
    :root {
        --color-bg-light: #1f2937;
        --color-bg-light-alt: #374151;
        --color-white: #1f2937;
        --color-text: #f9fafb;
        --color-text-light: #d1d5db;
    }
}

/* Modern Accessibility Enhancements */
@media (prefers-reduced-motion: reduce) {
    *, *::before, *::after {
        animation-duration: 0.01ms !important;
        animation-iteration-count: 1 !important;
        transition-duration: 0.01ms !important;
    }
}

/* Modern Print Styles */
@media print {
    .no-print {
        display: none !important;
    }
    
    * {
        background: transparent !important;
        color: black !important;
        box-shadow: none !important;
    }
}
