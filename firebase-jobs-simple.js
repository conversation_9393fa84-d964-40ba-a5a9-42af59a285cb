// Simple Firebase Jobs Manager (Non-module version)
// This version loads Firebase from CDN and creates a global jobManager

(function() {
    'use strict';
    
    // Wait for Firebase to be loaded
    function waitForFirebase() {
        return new Promise((resolve, reject) => {
            if (window.firebase) {
                resolve();
                return;
            }
            
            // Load Firebase from CDN
            const script = document.createElement('script');
            script.src = 'https://www.gstatic.com/firebasejs/9.22.0/firebase-app.js';
            script.type = 'module';
            script.onload = resolve;
            script.onerror = reject;
            document.head.appendChild(script);
        });
    }
    
    // Initialize Firebase Job Manager
    async function initializeFirebaseJobManager() {
        try {
            // Import Firebase modules
            const { initializeApp } = await import('https://www.gstatic.com/firebasejs/9.22.0/firebase-app.js');
            const { 
                getFirestore, 
                collection, 
                addDoc, 
                getDocs, 
                query, 
                where, 
                orderBy, 
                serverTimestamp 
            } = await import('https://www.gstatic.com/firebasejs/9.22.0/firebase-firestore.js');
            const { 
                getAuth, 
                onAuthStateChanged 
            } = await import('https://www.gstatic.com/firebasejs/9.22.0/firebase-auth.js');
            
            // Firebase configuration
            const firebaseConfig = {
                apiKey: "AIzaSyA2Is7jXaYL4k04tSaI-CRzmtisQ5VSmz4",
                authDomain: "shift-3140e.firebaseapp.com",
                projectId: "shift-3140e",
                storageBucket: "shift-3140e.firebasestorage.app",
                messagingSenderId: "716245939154",
                appId: "1:716245939154:web:64d567a1ded3fa98b34e0b",
                measurementId: "G-F6WJ0T3E71"
            };
            
            // Initialize Firebase
            const app = initializeApp(firebaseConfig);
            const db = getFirestore(app);
            const auth = getAuth(app);
            
            // Check if user is authenticated
            function checkAuthentication() {
                return new Promise((resolve, reject) => {
                    const unsubscribe = onAuthStateChanged(auth, (user) => {
                        unsubscribe();
                        if (user) {
                            console.log('🔐 User is authenticated:', user.uid, user.email);
                            resolve(user);
                        } else {
                            console.log('🔐 User not authenticated');
                            reject(new Error('User must be logged in to publish jobs. Please sign in first.'));
                        }
                    });
                });
            }
            
            // Job Manager Class
            class JobManager {
                constructor() {
                    this.db = db;
                    this.jobsCollection = 'jobs';
                    this.applicationsCollection = 'applications';
                }
                
                async publishJob(jobData) {
                    try {
                        console.log('📝 Publishing job to Firebase:', jobData);
                        
                        // Check authentication before publishing
                        console.log('🔐 Checking authentication...');
                        await checkAuthentication();
                        console.log('🔐 Authentication confirmed');
                        
                        const jobDoc = {
                            title: jobData.title,
                            description: jobData.description,
                            paymentCode: jobData.paymentCode,
                            expectedHours: jobData.expectedHours,
                            category: jobData.category,
                            categoryTitle: jobData.categoryTitle,
                            employerName: jobData.employerName,
                            employerEmail: jobData.employerEmail,
                            employerId: jobData.employerId || null,
                            status: 'active',
                            createdAt: serverTimestamp(),
                            updatedAt: serverTimestamp()
                        };
                        
                        console.log('🔍 Attempting to write to collection:', this.jobsCollection);
                        console.log('🔍 Document data:', jobDoc);
                        
                        const docRef = await addDoc(collection(this.db, this.jobsCollection), jobDoc);
                        console.log('✅ Job published with ID:', docRef.id);
                        
                        return {
                            success: true,
                            jobId: docRef.id,
                            message: 'Job published successfully!'
                        };
                    } catch (error) {
                        console.error('❌ Error publishing job:', error);
                        return {
                            success: false,
                            error: error.message,
                            code: error.code
                        };
                    }
                }
                
                async getJobsByCategory(category) {
                    try {
                        console.log('🔍 Fetching jobs for category:', category);
                        
                        const q = query(
                            collection(this.db, this.jobsCollection),
                            where('category', '==', category),
                            where('status', '==', 'active'),
                            orderBy('createdAt', 'desc')
                        );
                        
                        const querySnapshot = await getDocs(q);
                        const jobs = [];
                        
                        querySnapshot.forEach((doc) => {
                            jobs.push({
                                id: doc.id,
                                ...doc.data()
                            });
                        });
                        
                        console.log(`✅ Found ${jobs.length} jobs for category ${category}`);
                        return jobs;
                    } catch (error) {
                        console.error('❌ Error fetching jobs:', error);
                        return [];
                    }
                }
            }
            
            // Create global instance
            const jobManager = new JobManager();
            window.jobManager = jobManager;
            
            console.log('🔥 Firebase JobManager initialized successfully (simple version)');
            
            return jobManager;
            
        } catch (error) {
            console.error('❌ Failed to initialize Firebase JobManager:', error);
            throw error;
        }
    }
    
    // Initialize when DOM is ready
    if (document.readyState === 'loading') {
        document.addEventListener('DOMContentLoaded', initializeFirebaseJobManager);
    } else {
        initializeFirebaseJobManager();
    }
    
})();
