rules_version = '2';
service cloud.firestore {
  match /databases/{database}/documents {
    
    // Users collection - users can read/write their own data
    match /users/{userId} {
      allow read, write: if request.auth != null && request.auth.uid == userId;
      allow read: if request.auth != null; // Allow all authenticated users to read other users for chat
    }
    
    // Jobs collection - employers can create/update their jobs, everyone can read
    match /jobs/{jobId} {
      allow read: if request.auth != null;
      allow create: if request.auth != null && 
                   request.auth.uid != null &&
                   request.resource.data.employerId == request.auth.uid &&
                   request.resource.data.status == 'active';
      allow update, delete: if request.auth != null && 
                            request.auth.uid == resource.data.employerId;
    }
    
    // Job applications - students can apply, employers can view applications for their jobs
    match /applications/{applicationId} {
      allow read: if request.auth != null && 
                 (request.auth.uid == resource.data.studentId || 
                  request.auth.uid == resource.data.employerId);
      allow create: if request.auth != null && 
                   request.auth.uid == request.resource.data.studentId;
      allow update: if request.auth != null && 
                   request.auth.uid == resource.data.employerId;
    }
    
    // Chat conversations - participants can read/write their conversations
    match /conversations/{conversationId} {
      allow read, write: if request.auth != null && 
                        request.auth.uid in resource.data.participants;
      allow create: if request.auth != null && 
                   request.auth.uid in request.resource.data.participants;
      
      // Messages within conversations
      match /messages/{messageId} {
        allow read, write: if request.auth != null && 
                          request.auth.uid in get(/databases/$(database)/documents/conversations/$(conversationId)).data.participants;
        allow create: if request.auth != null && 
                     request.auth.uid == request.resource.data.senderId;
      }
    }
    
    // Company profiles (if you add this feature)
    match /companies/{companyId} {
      allow read: if request.auth != null;
      allow write: if request.auth != null && 
                   request.auth.uid == resource.data.ownerId;
    }
    
    // Deny all other requests
    match /{document=**} {
      allow read, write: if false;
    }
  }
}
