<?xml version="1.0" encoding="UTF-8"?>
<project version="4">
  <component name="AutoImportSettings">
    <option name="autoReloadType" value="SELECTIVE" />
  </component>
  <component name="ChangeListManager">
    <list default="true" id="03a938bf-1eec-4f38-86c1-a579706b560a" name="Changes" comment="">
      <change beforePath="$PROJECT_DIR$/Job-listing.html" beforeDir="false" afterPath="$PROJECT_DIR$/Job-listing.html" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/chat.html" beforeDir="false" afterPath="$PROJECT_DIR$/chat.html" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/employer-profile.html" beforeDir="false" afterPath="$PROJECT_DIR$/employer-profile.html" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/employer-signin.html" beforeDir="false" afterPath="$PROJECT_DIR$/employer-signin.html" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/employer-signup.html" beforeDir="false" afterPath="$PROJECT_DIR$/employer-signup.html" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/firebase-auth.js" beforeDir="false" afterPath="$PROJECT_DIR$/firebase-auth.js" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/job-catagory.html" beforeDir="false" afterPath="$PROJECT_DIR$/job-catagory.html" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/student-profile.js" beforeDir="false" afterPath="$PROJECT_DIR$/student-profile.js" afterDir="false" />
    </list>
    <option name="SHOW_DIALOG" value="false" />
    <option name="HIGHLIGHT_CONFLICTS" value="true" />
    <option name="HIGHLIGHT_NON_ACTIVE_CHANGELIST" value="false" />
    <option name="LAST_RESOLUTION" value="IGNORE" />
  </component>
  <component name="Git.Settings">
    <option name="RECENT_GIT_ROOT_PATH" value="$PROJECT_DIR$" />
  </component>
  <component name="ProjectColorInfo"><![CDATA[{
  "associatedIndex": 4
}]]></component>
  <component name="ProjectId" id="2zjMaZpsroDURRF5dA2avFwzzq2" />
  <component name="ProjectViewState">
    <option name="hideEmptyMiddlePackages" value="true" />
    <option name="showLibraryContents" value="true" />
  </component>
  <component name="PropertiesComponent"><![CDATA[{
  "keyToString": {
    "ModuleVcsDetector.initialDetectionPerformed": "true",
    "RunOnceActivity.ShowReadmeOnStart": "true",
    "RunOnceActivity.git.unshallow": "true",
    "git-widget-placeholder": "main",
    "ignore.virus.scanning.warn.message": "true",
    "last_opened_file_path": "C:/Users/<USER>/OneDrive/Documents/GitHub/Shift-",
    "node.js.detected.package.eslint": "true",
    "node.js.detected.package.tslint": "true",
    "node.js.selected.package.eslint": "(autodetect)",
    "node.js.selected.package.tslint": "(autodetect)",
    "nodejs_package_manager_path": "npm",
    "vue.rearranger.settings.migration": "true"
  }
}]]></component>
  <component name="SharedIndexes">
    <attachedChunks>
      <set>
        <option value="bundled-jdk-9823dce3aa75-fbdcb00ec9e3-intellij.indexing.shared.core-IU-251.26927.53" />
        <option value="bundled-js-predefined-d6986cc7102b-09060db00ec0-JavaScript-IU-251.26927.53" />
      </set>
    </attachedChunks>
  </component>
  <component name="TaskManager">
    <task active="true" id="Default" summary="Default task">
      <changelist id="03a938bf-1eec-4f38-86c1-a579706b560a" name="Changes" comment="" />
      <created>1752240094263</created>
      <option name="number" value="Default" />
      <option name="presentableId" value="Default" />
      <updated>1752240094263</updated>
      <workItem from="1752240097900" duration="1698000" />
    </task>
    <servers />
  </component>
  <component name="TypeScriptGeneratedFilesManager">
    <option name="version" value="3" />
  </component>
</project>