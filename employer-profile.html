<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Legal Career Platform - Employer Profile</title>
    <link rel="stylesheet" href="employer-profile-styles.css">
</head>
<body>
    <div class="container">
        <div class="profile-card">
            <div class="header">
                <div class="close-btn" onclick="goBack()">
                    <svg width="24" height="24" viewBox="0 0 24 24" fill="none">
                        <line x1="18" y1="6" x2="6" y2="18" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
                        <line x1="6" y1="6" x2="18" y2="18" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
                    </svg>
                </div>
            </div>

        <div class="profile-section">
            <div class="profile-avatar">
                <img id="company-logo" src="" alt="Company Logo" style="display:none;max-width:80px;max-height:80px;border-radius:8px;" />
                <svg id="default-avatar" width="60" height="60" viewBox="0 0 24 24" fill="none">
                    <path d="M20 21V19C20 17.9391 19.5786 16.9217 18.8284 16.1716C18.0783 15.4214 17.0609 15 16 15H8C6.93913 15 5.92172 15.4214 5.17157 16.1716C4.42143 16.9217 4 17.9391 4 19V21" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
                    <circle cx="12" cy="7" r="4" stroke="currentColor" stroke-width="2"/>
                </svg>
            </div>
            <h1 class="company-name" id="profile-company-name">Company Name</h1>
            <!-- Logout button -->
            <button id="logout-btn" style="margin-top: 24px; width: 100%; padding: 10px 0; background: #dc2626; color: #fff; border: none; border-radius: 6px; font-size: 1rem; cursor: pointer;">Logout</button>
        </div>

        <div class="menu-section">
            <div class="menu-item" onclick="showCompanyInfo()">
                <div class="menu-icon">
                    <svg width="24" height="24" viewBox="0 0 24 24" fill="none">
                        <path d="M3 21H21M5 21V7L13 2L21 7V21M9 9H11M9 12H11M9 15H11M13 9H15M13 12H15M13 15H15" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
                    </svg>
                </div>
                <span class="menu-text">Company Information</span>
                <div class="menu-arrow">
                    <svg width="20" height="20" viewBox="0 0 24 24" fill="none">
                        <polyline points="9,18 15,12 9,6" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
                    </svg>
                </div>
            </div>
            <div class="menu-item" onclick="showJobInfo()">
                <div class="menu-icon">
                    <svg width="24" height="24" viewBox="0 0 24 24" fill="none">
                        <path d="M20 7H4C2.89543 7 2 7.89543 2 9V19C2 20.1046 2.89543 21 4 21H20C21.1046 21 22 20.1046 22 19V9C22 7.89543 21.1046 7 20 7Z" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
                        <path d="M16 21V5C16 4.46957 15.7893 3.96086 15.4142 3.58579C15.0391 3.21071 14.5304 3 14 3H10C9.46957 3 8.96086 3.21071 8.58579 3.58579C8.21071 3.96086 8 4.46957 8 5V21" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
                    </svg>
                </div>
                <span class="menu-text">Job Postings</span>
                <div class="menu-arrow">
                    <svg width="20" height="20" viewBox="0 0 24 24" fill="none">
                        <polyline points="9,18 15,12 9,6" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
                    </svg>
                </div>
            </div>
            <div class="menu-item" onclick="viewApplications()">
                <div class="menu-icon">
                    <svg width="24" height="24" viewBox="0 0 24 24" fill="none">
                        <path d="M14 2H6C5.46957 2 4.96086 2.21071 4.58579 2.58579C4.21071 2.96086 4 3.46957 4 4V20C4 20.5304 4.21071 21.0391 4.58579 21.4142C4.96086 21.7893 5.46957 22 6 22H18C18.5304 22 19.0391 21.7893 19.4142 21.4142C19.7893 21.0391 20 20.5304 20 20V8L14 2Z" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
                        <polyline points="14,2 14,8 20,8" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
                    </svg>
                </div>
                <span class="menu-text">Job Applications</span>
                <div class="menu-arrow">
                    <svg width="20" height="20" viewBox="0 0 24 24" fill="none">
                        <polyline points="9,18 15,12 9,6" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
                    </svg>
                </div>
            </div>
        </div>

        <div class="settings-section">
                </svg>
                <!-- Settings removed -->
            </div>
        </div>
    </div>

    <!-- Modal for different sections -->
    <div class="modal-overlay" id="modal-overlay">
        <div class="modal-content">
            <div class="modal-header">
                <h2 id="modal-title">Section Title</h2>
                <button class="modal-close" onclick="closeModal()">✕</button>
            </div>
            <div class="modal-body" id="modal-body">
                <!-- Content will be dynamically loaded here -->
            </div>
        </div>
        </div>
    </div>

    <script>
        function goBack() {
            window.history.back();
        }

        // Load employer info from localStorage
        let employerInfo = null;
        try {
            employerInfo = JSON.parse(localStorage.getItem('employerInfo'));
        } catch (e) {}

        // Set profile section
        if (employerInfo) {
            document.getElementById('profile-company-name').textContent = employerInfo.name || 'Company Name';
            if (employerInfo.logo) {
                document.getElementById('company-logo').src = employerInfo.logo;
                document.getElementById('company-logo').style.display = 'block';
                document.getElementById('default-avatar').style.display = 'none';
            } else {
                document.getElementById('company-logo').style.display = 'none';
                document.getElementById('default-avatar').style.display = 'block';
            }
        }

        function showModal(title, content) {
            document.getElementById('modal-title').textContent = title;
            document.getElementById('modal-body').innerHTML = content;
            document.getElementById('modal-overlay').style.display = 'block';
            document.body.style.overflow = 'hidden';
        }

        function closeModal() {
            document.getElementById('modal-overlay').style.display = 'none';
            document.body.style.overflow = 'auto';
        }

        function showCompanyInfo() {
            if (!employerInfo) {
                showModal('Company Information', '<div>No company information available.</div>');
                return;
            }
            const content = `
                <div class="info-section">
                    <div class="info-item">
                        <strong>Company Name:</strong>
                        <span>${employerInfo.name || ''}</span>
                    </div>
                    <div class="info-item">
                        <strong>Type:</strong>
                        <span>${employerInfo.type || ''}</span>
                    </div>
                    <div class="info-item">
                        <strong>Phone:</strong>
                        <span>${employerInfo.phone || ''}</span>
                    </div>
                    <div class="info-item">
                        <strong>Email:</strong>
                        <span>${employerInfo.email || ''}</span>
                    </div>
                </div>
            `;
            showModal('Company Information', content);
        }

        function showJobInfo() {
            // Show jobs posted by this employer
            const jobs = JSON.parse(localStorage.getItem('jobs') || '[]');
            const employer = employerInfo || {};
            const myJobs = jobs.filter(job => job.employerEmail === employer.email);
            if (!myJobs.length) {
                showModal('Job Postings', '<div>No job postings yet. Add jobs from the Add-Job section.</div>');
                return;
            }
            let content = '<div class="info-section">';
            myJobs.forEach(job => {
                content += `
                    <div class="job-card">
                        <div><strong>Title:</strong> ${job.title}</div>
                        <div><strong>Category:</strong> ${job.categoryTitle || job.category}</div>
                        <div><strong>Payment:</strong> ${job.paymentCode}</div>
                        <div><strong>Expected Hours:</strong> ${job.expectedHours || ''}</div>
                        <div><strong>Description:</strong> ${job.description}</div>
                    </div>
                    <hr />
                `;
            });
            content += '</div>';
            showModal('Job Postings', content);
        }

        function viewApplications() {
            const applications = JSON.parse(localStorage.getItem('jobApplications') || '[]');
            const currentEmployer = employerInfo || {};

            // Filter applications for this employer
            const myApplications = applications.filter(app =>
                app.employerEmail === currentEmployer.email ||
                app.employerName === currentEmployer.name
            );

            if (myApplications.length === 0) {
                showModal('Job Applications', '<p>No applications received yet.</p>');
                return;
            }

            let applicationsHTML = '<div style="max-height: 400px; overflow-y: auto;">';
            myApplications.forEach((app, index) => {
                applicationsHTML += `
                    <div style="border: 1px solid #ddd; padding: 15px; margin: 10px 0; border-radius: 8px; background: #f9f9f9;">
                        <h4>${app.jobTitle}</h4>
                        <p><strong>Applicant:</strong> ${app.studentName}</p>
                        <p><strong>Email:</strong> ${app.studentEmail}</p>
                        <p><strong>University:</strong> ${app.studentUniversity}</p>
                        <p><strong>Year of Study:</strong> ${app.studentYear}</p>
                        <p><strong>Department:</strong> ${app.studentDepartment}</p>
                        <p><strong>Applied:</strong> ${new Date(app.appliedAt).toLocaleDateString()}</p>
                        <p><strong>Status:</strong> <span style="color: green;">${app.status}</span></p>
                        <div style="margin-top: 10px;">
                            <button onclick="contactStudent('${app.studentEmail}', '${app.studentName}')" style="background: #d17e7e; color: white; border: none; padding: 8px 16px; border-radius: 4px; cursor: pointer; margin-right: 10px;">
                                Contact Student
                            </button>
                            <button onclick="updateApplicationStatus(${index}, 'Reviewed')" style="background: #5a3e5d; color: white; border: none; padding: 8px 16px; border-radius: 4px; cursor: pointer;">
                                Mark as Reviewed
                            </button>
                        </div>
                    </div>
                `;
            });
            applicationsHTML += '</div>';

            showModal('Job Applications', applicationsHTML);
        }

        function contactStudent(email, name) {
            const subject = encodeURIComponent('Regarding Your Job Application');
            const body = encodeURIComponent(`Dear ${name},\\n\\nThank you for your application. We would like to discuss the opportunity further.\\n\\nBest regards,\\n${employerInfo?.name || 'Employer'}`);
            window.open(`mailto:${email}?subject=${subject}&body=${body}`);
        }

        function updateApplicationStatus(index, status) {
            const applications = JSON.parse(localStorage.getItem('jobApplications') || '[]');
            if (applications[index]) {
                applications[index].status = status;
                localStorage.setItem('jobApplications', JSON.stringify(applications));
                alert(`Application status updated to: ${status}`);
                viewApplications(); // Refresh the view
            }
        }

        // Close modal with Escape key
        document.addEventListener('keydown', function(e) {
            if (e.key === 'Escape') {
                closeModal();
            }
        });

        // Logout logic
        document.getElementById('logout-btn').addEventListener('click', function() {
            localStorage.removeItem('employerInfo');
            window.location.href = 'employer-signin.html';
        });
    </script>
    <script>
document.addEventListener('DOMContentLoaded', function() {
    const user = JSON.parse(localStorage.getItem('currentUser') || '{}');
    if (user.userType !== 'employer') {
        alert('Access denied. Only employers can access this page.');
        window.location.href = 'employer-signin.html';
    }
});
</script>
    <!-- Chat Widget Integration -->
    <script src="chat-integration-widget.js"></script>
</body>
</html>
