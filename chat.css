/* Chat Interface Styles - Telegram-like Design */

:root {
    --color-primary: #d17e7e;
    --color-primary-dark: #c06c6c;
    --color-secondary: #5a3e5d;
    --color-secondary-light: #8c6c8e;
    --color-bg-light: #f8f0eb;
    --color-white: #ffffff;
    --color-text: #333333;
    --color-text-light: #666666;
    --color-text-muted: #999999;
    --color-border: #e1e5e9;
    --color-success: #28a745;
    --color-danger: #dc3545;
    --color-warning: #ffc107;
    --color-info: #17a2b8;
    
    /* Chat specific colors */
    --chat-bg: #f5f7fa;
    --sidebar-bg: #ffffff;
    --message-sent: #d17e7e;
    --message-received: #ffffff;
    --message-text-sent: #ffffff;
    --message-text-received: #333333;
    --online-color: #4caf50;
    --typing-color: #2196f3;
}

* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Oxygen, Ubuntu, Cantarell, sans-serif;
    background: var(--chat-bg);
    height: 100vh;
    overflow: hidden;
}

/* Main Chat Container */
.chat-container {
    display: flex;
    height: 100vh;
    max-width: 1400px;
    margin: 0 auto;
    background: var(--color-white);
    box-shadow: 0 0 20px rgba(0, 0, 0, 0.1);
}

/* Chat Sidebar */
.chat-sidebar {
    width: 350px;
    background: var(--sidebar-bg);
    border-right: 1px solid var(--color-border);
    display: flex;
    flex-direction: column;
    height: 100vh;
}

.sidebar-header {
    padding: 20px;
    border-bottom: 1px solid var(--color-border);
    display: flex;
    align-items: center;
    justify-content: space-between;
}

.user-info {
    display: flex;
    align-items: center;
    gap: 12px;
}

.user-avatar {
    position: relative;
}

.user-avatar img {
    width: 45px;
    height: 45px;
    border-radius: 50%;
    object-fit: cover;
}

.online-indicator {
    position: absolute;
    bottom: 2px;
    right: 2px;
    width: 12px;
    height: 12px;
    background: var(--online-color);
    border: 2px solid var(--color-white);
    border-radius: 50%;
}

.user-details h3 {
    font-size: 16px;
    font-weight: 600;
    color: var(--color-text);
    margin-bottom: 2px;
}

.user-type {
    font-size: 12px;
    color: var(--color-text-muted);
    text-transform: capitalize;
}

.sidebar-actions {
    display: flex;
    gap: 8px;
}

.action-btn {
    width: 36px;
    height: 36px;
    border: none;
    background: var(--color-bg-light);
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    cursor: pointer;
    transition: all 0.2s ease;
    color: var(--color-text-light);
}

.action-btn:hover {
    background: var(--color-primary);
    color: var(--color-white);
}

/* Search Container */
.search-container {
    padding: 15px 20px;
    border-bottom: 1px solid var(--color-border);
}

.search-input-wrapper {
    position: relative;
    display: flex;
    align-items: center;
}

.search-icon {
    position: absolute;
    left: 12px;
    color: var(--color-text-muted);
    font-size: 14px;
}

.search-input {
    width: 100%;
    padding: 10px 12px 10px 35px;
    border: 1px solid var(--color-border);
    border-radius: 20px;
    font-size: 14px;
    background: var(--color-bg-light);
    transition: all 0.2s ease;
}

.search-input:focus {
    outline: none;
    border-color: var(--color-primary);
    background: var(--color-white);
}

.search-clear {
    position: absolute;
    right: 8px;
    width: 24px;
    height: 24px;
    border: none;
    background: none;
    border-radius: 50%;
    cursor: pointer;
    color: var(--color-text-muted);
}

/* Conversations Container */
.conversations-container {
    flex: 1;
    overflow-y: auto;
}

.conversations-list {
    padding: 0;
}

.conversation-item {
    display: flex;
    align-items: center;
    padding: 15px 20px;
    cursor: pointer;
    transition: background-color 0.2s ease;
    border-bottom: 1px solid rgba(0, 0, 0, 0.05);
}

.conversation-item:hover {
    background: var(--color-bg-light);
}

.conversation-item.active {
    background: var(--color-primary);
    color: var(--color-white);
}

.conversation-avatar {
    position: relative;
    margin-right: 12px;
}

.conversation-avatar img {
    width: 50px;
    height: 50px;
    border-radius: 50%;
    object-fit: cover;
}

.conversation-info {
    flex: 1;
    min-width: 0;
}

.conversation-name {
    font-size: 15px;
    font-weight: 600;
    margin-bottom: 4px;
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
}

.conversation-preview {
    font-size: 13px;
    color: var(--color-text-light);
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
}

.conversation-item.active .conversation-preview {
    color: rgba(255, 255, 255, 0.8);
}

.conversation-meta {
    display: flex;
    flex-direction: column;
    align-items: flex-end;
    gap: 4px;
}

.conversation-time {
    font-size: 12px;
    color: var(--color-text-muted);
}

.conversation-item.active .conversation-time {
    color: rgba(255, 255, 255, 0.8);
}

.unread-badge {
    background: var(--color-primary);
    color: var(--color-white);
    font-size: 11px;
    font-weight: 600;
    padding: 2px 6px;
    border-radius: 10px;
    min-width: 18px;
    text-align: center;
}

.conversation-item.active .unread-badge {
    background: var(--color-white);
    color: var(--color-primary);
}

/* Loading States */
.loading-conversations {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    padding: 40px 20px;
    color: var(--color-text-muted);
}

.loading-spinner {
    width: 24px;
    height: 24px;
    border: 2px solid var(--color-border);
    border-top: 2px solid var(--color-primary);
    border-radius: 50%;
    animation: spin 1s linear infinite;
    margin-bottom: 10px;
}

@keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

/* Chat Main Area */
.chat-main {
    flex: 1;
    display: flex;
    flex-direction: column;
    background: var(--chat-bg);
}

/* Welcome Screen */
.welcome-screen {
    display: flex;
    align-items: center;
    justify-content: center;
    height: 100%;
    background: var(--color-white);
}

.welcome-content {
    text-align: center;
    max-width: 400px;
    padding: 40px;
}

.welcome-icon {
    font-size: 64px;
    color: var(--color-primary);
    margin-bottom: 20px;
}

.welcome-content h2 {
    font-size: 28px;
    color: var(--color-text);
    margin-bottom: 10px;
}

.welcome-content p {
    color: var(--color-text-light);
    margin-bottom: 30px;
    line-height: 1.5;
}

.start-chat-btn {
    background: var(--color-primary);
    color: var(--color-white);
    border: none;
    padding: 12px 24px;
    border-radius: 25px;
    font-size: 14px;
    font-weight: 600;
    cursor: pointer;
    transition: all 0.2s ease;
    display: flex;
    align-items: center;
    gap: 8px;
    margin: 0 auto;
}

.start-chat-btn:hover {
    background: var(--color-primary-dark);
    transform: translateY(-1px);
}

/* Chat Area */
.chat-area {
    display: flex;
    flex-direction: column;
    height: 100%;
}

/* Chat Header */
.chat-header {
    background: var(--color-white);
    padding: 15px 20px;
    border-bottom: 1px solid var(--color-border);
    display: flex;
    align-items: center;
    gap: 15px;
}

.back-btn {
    display: none;
    width: 36px;
    height: 36px;
    border: none;
    background: none;
    border-radius: 50%;
    cursor: pointer;
    color: var(--color-text-light);
    transition: all 0.2s ease;
}

.back-btn:hover {
    background: var(--color-bg-light);
}

.chat-user-info {
    display: flex;
    align-items: center;
    gap: 12px;
    flex: 1;
}

.chat-user-avatar {
    position: relative;
}

.chat-user-avatar img {
    width: 40px;
    height: 40px;
    border-radius: 50%;
    object-fit: cover;
}

.chat-user-details h3 {
    font-size: 16px;
    font-weight: 600;
    color: var(--color-text);
    margin-bottom: 2px;
}

.user-status {
    font-size: 12px;
    color: var(--color-text-muted);
}

.user-status.online {
    color: var(--online-color);
}

.user-status.typing {
    color: var(--typing-color);
}

.chat-actions {
    display: flex;
    gap: 8px;
}

/* Messages Container */
.messages-container {
    flex: 1;
    overflow-y: auto;
    padding: 20px;
    background: var(--chat-bg);
}

.messages-list {
    display: flex;
    flex-direction: column;
    gap: 8px;
}

/* Message Bubbles */
.message {
    display: flex;
    margin-bottom: 8px;
}

.message.sent {
    justify-content: flex-end;
}

.message.received {
    justify-content: flex-start;
}

.message-bubble {
    max-width: 70%;
    padding: 10px 15px;
    border-radius: 18px;
    position: relative;
    word-wrap: break-word;
}

.message.sent .message-bubble {
    background: var(--message-sent);
    color: var(--message-text-sent);
    border-bottom-right-radius: 4px;
}

.message.received .message-bubble {
    background: var(--message-received);
    color: var(--message-text-received);
    border-bottom-left-radius: 4px;
    box-shadow: 0 1px 2px rgba(0, 0, 0, 0.1);
}

.message-text {
    font-size: 14px;
    line-height: 1.4;
    margin-bottom: 4px;
}

.message-time {
    font-size: 11px;
    opacity: 0.7;
    text-align: right;
}

.message.received .message-time {
    text-align: left;
}

/* Typing Indicator */
.typing-indicator {
    display: flex;
    align-items: center;
    gap: 10px;
    padding: 10px 0;
}

.typing-avatar img {
    width: 30px;
    height: 30px;
    border-radius: 50%;
}

.typing-animation {
    display: flex;
    gap: 3px;
    padding: 8px 12px;
    background: var(--color-white);
    border-radius: 15px;
    box-shadow: 0 1px 2px rgba(0, 0, 0, 0.1);
}

.typing-animation span {
    width: 6px;
    height: 6px;
    background: var(--color-text-muted);
    border-radius: 50%;
    animation: typing 1.4s infinite ease-in-out;
}

.typing-animation span:nth-child(2) {
    animation-delay: 0.2s;
}

.typing-animation span:nth-child(3) {
    animation-delay: 0.4s;
}

@keyframes typing {
    0%, 60%, 100% {
        transform: translateY(0);
        opacity: 0.5;
    }
    30% {
        transform: translateY(-10px);
        opacity: 1;
    }
}

/* Message Input */
.message-input-container {
    background: var(--color-white);
    padding: 15px 20px;
    border-top: 1px solid var(--color-border);
}

/* File preview styles */
.file-preview-container {
    background: #f8f9fa;
    border: 1px solid #e9ecef;
    border-radius: 8px;
    margin-bottom: 15px;
    padding: 10px;
}

.file-preview-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 10px;
    font-size: 14px;
    font-weight: 500;
}

.clear-files-btn {
    background: #dc3545;
    color: white;
    border: none;
    padding: 4px 8px;
    border-radius: 4px;
    font-size: 12px;
    cursor: pointer;
    transition: background-color 0.2s;
}

.clear-files-btn:hover {
    background: #c82333;
}

.file-preview-list {
    display: flex;
    flex-wrap: wrap;
    gap: 8px;
}

.file-preview-item {
    display: flex;
    align-items: center;
    background: white;
    border: 1px solid #dee2e6;
    border-radius: 6px;
    padding: 8px 12px;
    font-size: 13px;
    max-width: 200px;
}

.file-preview-icon {
    margin-right: 8px;
    font-size: 16px;
}

.file-preview-name {
    flex: 1;
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
}

.file-preview-size {
    margin-left: 8px;
    color: #6c757d;
    font-size: 11px;
}

.file-remove-btn {
    background: none;
    border: none;
    color: #dc3545;
    cursor: pointer;
    margin-left: 8px;
    padding: 0;
    font-size: 14px;
}

.file-remove-btn:hover {
    color: #c82333;
}

.message-input-wrapper {
    display: flex;
    align-items: flex-end;
    gap: 10px;
}

.attachment-btn {
    width: 40px;
    height: 40px;
    border: none;
    background: var(--color-bg-light);
    border-radius: 50%;
    cursor: pointer;
    color: var(--color-text-light);
    transition: all 0.2s ease;
}

.attachment-btn:hover {
    background: var(--color-primary);
    color: var(--color-white);
}

.input-field-wrapper {
    flex: 1;
    position: relative;
    background: var(--color-bg-light);
    border-radius: 20px;
    display: flex;
    align-items: flex-end;
}

#messageInput {
    flex: 1;
    border: none;
    background: none;
    padding: 10px 45px 10px 15px;
    font-size: 14px;
    line-height: 1.4;
    resize: none;
    max-height: 120px;
    min-height: 40px;
    outline: none;
    font-family: inherit;
}

.emoji-btn {
    position: absolute;
    right: 8px;
    bottom: 8px;
    width: 24px;
    height: 24px;
    border: none;
    background: none;
    cursor: pointer;
    color: var(--color-text-muted);
    border-radius: 50%;
    transition: all 0.2s ease;
}

.emoji-btn:hover {
    color: var(--color-primary);
}

.send-btn {
    width: 40px;
    height: 40px;
    border: none;
    background: var(--color-primary);
    border-radius: 50%;
    cursor: pointer;
    color: var(--color-white);
    transition: all 0.2s ease;
}

.send-btn:hover {
    background: var(--color-primary-dark);
    transform: scale(1.05);
}

.send-btn:disabled {
    background: var(--color-text-muted);
    cursor: not-allowed;
    transform: none;
}

.input-actions {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-top: 8px;
    font-size: 12px;
    color: var(--color-text-muted);
}

/* Responsive Design */
@media (max-width: 768px) {
    .chat-container {
        height: 100vh;
    }
    
    .chat-sidebar {
        position: absolute;
        left: 0;
        top: 0;
        height: 100%;
        z-index: 1000;
        transform: translateX(-100%);
        transition: transform 0.3s ease;
    }
    
    .chat-sidebar.open {
        transform: translateX(0);
    }
    
    .chat-main {
        width: 100%;
    }
    
    .back-btn {
        display: flex;
    }
    
    .message-bubble {
        max-width: 85%;
    }
}

/* Modal Styles */
.modal-overlay {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: rgba(0, 0, 0, 0.5);
    display: flex;
    align-items: center;
    justify-content: center;
    z-index: 2000;
}

.modal {
    background: var(--color-white);
    border-radius: 10px;
    max-width: 500px;
    width: 90%;
    max-height: 80vh;
    overflow: hidden;
    box-shadow: 0 10px 30px rgba(0, 0, 0, 0.3);
}

.modal-header {
    padding: 20px;
    border-bottom: 1px solid var(--color-border);
    display: flex;
    align-items: center;
    justify-content: space-between;
}

.modal-header h3 {
    font-size: 18px;
    font-weight: 600;
    color: var(--color-text);
}

.modal-close {
    width: 30px;
    height: 30px;
    border: none;
    background: none;
    border-radius: 50%;
    cursor: pointer;
    color: var(--color-text-muted);
    transition: all 0.2s ease;
}

.modal-close:hover {
    background: var(--color-bg-light);
    color: var(--color-text);
}

.modal-body {
    padding: 20px;
    max-height: 60vh;
    overflow-y: auto;
}

.modal-footer {
    padding: 15px 20px;
    border-top: 1px solid var(--color-border);
    display: flex;
    gap: 10px;
    justify-content: flex-end;
}

/* Button Styles */
.btn {
    padding: 8px 16px;
    border: none;
    border-radius: 6px;
    font-size: 14px;
    font-weight: 500;
    cursor: pointer;
    transition: all 0.2s ease;
}

.btn-primary {
    background: var(--color-primary);
    color: var(--color-white);
}

.btn-primary:hover {
    background: var(--color-primary-dark);
}

.btn-secondary {
    background: var(--color-bg-light);
    color: var(--color-text);
}

.btn-secondary:hover {
    background: var(--color-border);
}

/* Loading Overlay */
.loading-overlay {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: rgba(255, 255, 255, 0.9);
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    z-index: 3000;
    font-size: 14px;
    color: var(--color-text-muted);
}

.loading-overlay .loading-spinner {
    width: 40px;
    height: 40px;
    margin-bottom: 15px;
}

/* File Upload Styles */
.file-upload-area {
    border: 2px dashed var(--color-border);
    border-radius: 10px;
    padding: 40px;
    text-align: center;
    color: var(--color-text-muted);
    cursor: pointer;
    transition: all 0.2s ease;
}

.file-upload-area:hover {
    border-color: var(--color-primary);
    color: var(--color-primary);
}

.file-upload-area.dragover {
    border-color: var(--color-primary);
    background: rgba(209, 126, 126, 0.1);
}

.file-upload-area i {
    font-size: 48px;
    margin-bottom: 15px;
    display: block;
}

.file-upload-area input[type="file"] {
    display: none;
}

.file-preview {
    margin-top: 20px;
}

.file-item {
    display: flex;
    align-items: center;
    gap: 10px;
    padding: 10px;
    border: 1px solid var(--color-border);
    border-radius: 6px;
    margin-bottom: 10px;
}

.file-icon {
    width: 40px;
    height: 40px;
    background: var(--color-bg-light);
    border-radius: 6px;
    display: flex;
    align-items: center;
    justify-content: center;
    color: var(--color-text-muted);
}

.file-info {
    flex: 1;
}

.file-name {
    font-size: 14px;
    font-weight: 500;
    color: var(--color-text);
    margin-bottom: 2px;
}

.file-size {
    font-size: 12px;
    color: var(--color-text-muted);
}

.file-remove {
    width: 24px;
    height: 24px;
    border: none;
    background: none;
    border-radius: 50%;
    cursor: pointer;
    color: var(--color-text-muted);
    transition: all 0.2s ease;
}

.file-remove:hover {
    background: var(--color-danger);
    color: var(--color-white);
}

/* User Search Styles */
.search-users input {
    width: 100%;
    padding: 10px 15px;
    border: 1px solid var(--color-border);
    border-radius: 8px;
    font-size: 14px;
    margin-bottom: 15px;
}

.users-list {
    max-height: 300px;
    overflow-y: auto;
}

.user-item {
    display: flex;
    align-items: center;
    gap: 12px;
    padding: 12px;
    cursor: pointer;
    border-radius: 8px;
    transition: all 0.2s ease;
    border: 1px solid transparent;
}

.user-item:hover {
    background: var(--color-bg-light);
    border-color: var(--color-primary);
    transform: translateY(-1px);
    box-shadow: 0 2px 8px rgba(209, 126, 126, 0.2);
}

.user-item:active {
    transform: translateY(0);
    background: var(--color-primary);
    color: white;
}

.user-item-avatar img {
    width: 40px;
    height: 40px;
    border-radius: 50%;
    object-fit: cover;
}

.user-item-info {
    flex: 1;
}

.user-item-name {
    font-size: 14px;
    font-weight: 500;
    color: var(--color-text);
    margin-bottom: 2px;
}

.user-item-email {
    font-size: 12px;
    color: var(--color-text-muted);
}

.user-item-type {
    font-size: 11px;
    padding: 2px 6px;
    border-radius: 10px;
    background: var(--color-bg-light);
    color: var(--color-text-muted);
    text-transform: capitalize;
}
