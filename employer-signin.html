<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Employer Sign In</title>
    <link rel="stylesheet" href="employer-signin-styles.css">
</head>
<body>
    <div class="container">
        <div class="signin-card">
            <div class="header">
                <div class="logo-container">
                    <div class="logo">S</div>
                </div>
            </div>
            
            <div class="content">
                <h1 class="page-title">Welcome Back</h1>
                <p class="subtitle">Sign in to your employer account</p>
                
                <form id="employer-signin-form" class="signin-form">
                    <div class="form-field">
                        <input type="email" id="employer-email" placeholder="Email" required>
                    </div>
                    
                    <div class="form-field">
                        <input type="password" id="employer-password" placeholder="Password" required>
                    </div>
                    
                    <div class="form-options">
                        <label class="remember-me">
                            <input type="checkbox" id="remember-me">
                            <span class="checkmark"></span>
                            Remember me
                        </label>
                        <a href="#" class="forgot-password">Forgot Password?</a>
                    </div>
                    
                    <button type="submit" class="btn-signin">Sign In</button>
                </form>

                <div class="divider">
                    <span>or</span>
                </div>

                <button class="btn-google" onclick="handleGoogleSignIn()">
                    <svg class="google-icon" viewBox="0 0 24 24" width="18" height="18">
                        <path fill="#4285F4" d="M22.56 12.25c0-.78-.07-1.53-.2-2.25H12v4.26h5.92c-.26 1.37-1.04 2.53-2.21 3.31v2.77h3.57c2.08-1.92 3.28-4.74 3.28-8.09z"/>
                        <path fill="#34A853" d="M12 23c2.97 0 5.46-.98 7.28-2.66l-3.57-2.77c-.98.66-2.23 1.06-3.71 1.06-2.86 0-5.29-1.93-6.16-4.53H2.18v2.84C3.99 20.53 7.7 23 12 23z"/>
                        <path fill="#FBBC05" d="M5.84 14.09c-.22-.66-.35-1.36-.35-2.09s.13-1.43.35-2.09V7.07H2.18C1.43 8.55 1 10.22 1 12s.43 3.45 1.18 4.93l2.85-2.22.81-.62z"/>
                        <path fill="#EA4335" d="M12 5.38c1.62 0 3.06.56 4.21 1.64l3.15-3.15C17.45 2.09 14.97 1 12 1 7.7 1 3.99 3.47 2.18 7.07l3.66 2.84c.87-2.6 3.3-4.53 6.16-4.53z"/>
                    </svg>
                    Sign In with Google
                </button>
                
                <div class="signup-link">
                    <p>Don't have an account? <a href="employer-signup.html">Sign Up</a></p>
                </div>
            </div>
        </div>
    </div>

    <script>
        document.getElementById('employer-signin-form').addEventListener('submit', function(e) {
            e.preventDefault();
            
            const email = document.getElementById('employer-email').value;
            const password = document.getElementById('employer-password').value;
            
            if (email && password) {
                alert(`Welcome back! You have been signed in successfully.`);
                // Redirect to dashboard or main page
                // window.location.href = 'dashboard.html';
            } else {
                alert('Please fill in all required fields');
            }
        });

        function handleGoogleSignIn() {
            // Google sign-in functionality would be implemented here
        }

        // Form validation
        const inputs = document.querySelectorAll('input[type="email"], input[type="password"]');
        inputs.forEach(input => {
            input.addEventListener('blur', function() {
                if (this.hasAttribute('required') && !this.value) {
                    this.style.borderColor = '#dc2626';
                } else if (this.type === 'email' && this.value) {
                    const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
                    if (!emailRegex.test(this.value)) {
                        this.style.borderColor = '#dc2626';
                    } else {
                        this.style.borderColor = 'var(--color-primary)';
                    }
                } else {
                    this.style.borderColor = '#d1d5db';
                }
            });
        });
    </script>
</body>
</html>
